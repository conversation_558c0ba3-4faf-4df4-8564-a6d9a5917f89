package com.challanty.android.kp3.service

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Canvas
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.PaintingStyle
import androidx.compose.ui.unit.IntSize
import com.challanty.android.kp3.pictureUnits.original.CelticPicUnitFactory
import com.challanty.android.kp3.pictureUnits.original.CelticPicUnitStrategy
import com.challanty.android.kp3.puzzle.CelticKnotPuzzle
import com.challanty.android.kp3.state.ProcessingStateManager
import com.challanty.android.kp3.state.ProcessingType
import com.challanty.android.kp3.util.BG_PIC_UNIT_BOX_SIZE
import com.challanty.android.kp3.util.toneOnToneColor
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service for managing the background puzzle generation and state.
 * Handles generation and state management of the Celtic knot background pattern.
 * This service can be safely injected into multiple ViewModels.
 * Note: ViewModels can't be injected into other ViewModels.
 */
@Singleton
class BackgroundService @Inject constructor(
    private val processingStateManager: ProcessingStateManager,
    private val coroutineScope: CoroutineScope
) {

    // The background color for the puzzle
    // This is set from AppRoot with MaterialTheme.colorScheme.background
    private var backgroundColor: Color? = null

    // A size in pixels for small versions of the 26 Celtic knot picture units
    // where the Celtic knots can still be discerned.
    private val picUnitSize = BG_PIC_UNIT_BOX_SIZE

    // StateFlow for keeping the background up-to-date
    private val _backgroundBitmap = MutableStateFlow<ImageBitmap?>(null)
    val backgroundBitmap: StateFlow<ImageBitmap?> = _backgroundBitmap.asStateFlow()

    private var curPuzDisplaySize = IntSize.Zero

    /**
     * Updates the background color and regenerates the background if needed.
     * This method should be called from a composable context where MaterialTheme is available.
     *
     * @param color The new background color
     */
    fun updateBackgroundColor(color: Color) {
        if (backgroundColor != color) {
            backgroundColor = color

            // Generate the background if we have a valid size
            if (curPuzDisplaySize != IntSize.Zero) {
                generateBackground()
            }
        }
    }

    fun onBGDisplaySizeChanged(size: IntSize) {
        // Ignore startup and redundant size changes
        if (size == IntSize.Zero || size == curPuzDisplaySize) return

        curPuzDisplaySize = size

        generateBackground()
    }

    fun generateBackground() {

        // Calculate how many cells we need to cover the screen
        var cols = (curPuzDisplaySize.width / picUnitSize).toInt() + 2
        var rows = (curPuzDisplaySize.height / picUnitSize).toInt() + 2

        // Rows and columns must be even numbers for generator to work
        if (cols % 2 != 0) cols++
        if (rows % 2 != 0) rows++

        coroutineScope.launch {

            processingStateManager.startProcessing(ProcessingType.BACKGROUND)

            val puzzle = CelticKnotPuzzle()
            puzzle.makePuzzleWithWalls(
                rows = rows,
                cols = cols,
                tileRows = 1,
                tileCols = 1,
                isUnify = true
            )

            generateBitmap(puzzle, curPuzDisplaySize, backgroundColor ?: Color.Gray)

            processingStateManager.endProcessing(ProcessingType.BACKGROUND)
        }
    }

    private fun generateBitmap(
        puzzle: CelticKnotPuzzle,
        size: IntSize,
        backgroundColor: Color
    ) {
        // NOTE: We should already be on a background thread here
        // and BG_STARTUP processing should be active.
        // We are guaranteed to have a valid puzzle at this point

        val bitmap = ImageBitmap(size.width, size.height)
        val canvas = Canvas(bitmap)

        // Fill with background color first
        canvas.drawRect(
            0f, 0f, size.width.toFloat(), size.height.toFloat(),
            Paint().apply {
                this.color = backgroundColor
            }
        )

        drawBackgroundPuzzle(
            canvas = canvas,
            size = size,
            backgroundColor = backgroundColor,
            puzzle = puzzle
        )

        _backgroundBitmap.value = bitmap
    }

    private fun drawBackgroundPuzzle(
        canvas: Canvas,
        size: IntSize,
        backgroundColor: Color,
        puzzle: CelticKnotPuzzle,
    ) {
        val solution = puzzle.solution
        val rows = puzzle.rows
        val cols = puzzle.cols

        // Calculate the offset to center the puzzle
        val totalWidth = cols * picUnitSize
        val totalHeight = rows * picUnitSize
        val offsetX = (size.width - totalWidth) / 2
        val offsetY = (size.height - totalHeight) / 2

        val colorPair = toneOnToneColor(backgroundColor)

        // Create reusable paint objects to avoid creating new ones for each drawing
        val fillPaint = Paint().apply {
            this.color = colorPair.first
            this.style = PaintingStyle.Fill
        }

        val strokePaint = Paint().apply {
            this.color = colorPair.second
            this.style = PaintingStyle.Stroke
            this.strokeWidth = 2f
        }

        for (row in 0 until rows) {
            for (col in 0 until cols) {
                val x = offsetX + col * picUnitSize
                val y = offsetY + row * picUnitSize

                val picUnitID = solution[row][col]
                val picUnitStrategy = CelticPicUnitFactory.createPicUnit(picUnitID)

                drawCelticPattern(
                    canvas = canvas,
                    picUnitStrategy = picUnitStrategy,
                    x = x,
                    y = y,
                    cellSize = picUnitSize,
                    fillPaint = fillPaint,
                    strokePaint = strokePaint
                )
            }
        }
    }

    /**
     * Draws a Celtic pattern on a Canvas.
     *
     * @param canvas The canvas to draw on
     * @param picUnitStrategy The strategy for creating the picture unit paths
     * @param x The x-coordinate of the pattern
     * @param y The y-coordinate of the pattern
     * @param cellSize The size of the cell
     * @param fillPaint The paint for filling the pattern
     * @param strokePaint The paint for stroking the pattern
     */
    private fun drawCelticPattern(
        canvas: Canvas,
        picUnitStrategy: CelticPicUnitStrategy,
        x: Float,
        y: Float,
        cellSize: Float,
        fillPaint: Paint,
        strokePaint: Paint
    ) {
        canvas.save()

        canvas.translate(x, y)

        // Calculate the center point in local coordinates
        val localCenterX = cellSize / 2
        val localCenterY = cellSize / 2
        val radius = cellSize / 2

        // TODO cache the paths?
        val paths = picUnitStrategy.createPaths(Offset(localCenterX, localCenterY), radius)

        paths.getRibbonPath().let { path ->
            canvas.drawPath(path, fillPaint)
        }

        paths.getOutlinePath().let { path ->
            canvas.drawPath(path, strokePaint)
        }

        canvas.restore()
    }
}
