package com.challanty.android.kp3.puzzle

import com.challanty.android.kp3.viewModel.TileModel

class CelticKnotPuzzle() {
    var scrambled: Array<IntArray> = Array(0) { IntArray(0) }
    var solution: Array<IntArray> = Array(0) { IntArray(0) }
    var rows: Int = 0
    var cols: Int = 0
    var tileRows: Int = 0
    var tileCols: Int = 0
    var doRotations: Boolean = false
    var seed: Long? = null

    private val walledPuzzleGenerator = CelticKnotWalledPuzzleGenerator()
    private val notWalledPuzzleGenerator = CelticKnotNotWalledPuzzleGenerator()
    private val puzzleService = CelticKnotPuzzleService()
    private val puzzleSolver = CelticKnotPuzzleSolver()

    fun swapTiles(
        boardRow1: Int,
        boardCol1: Int,
        boardRow2: Int,
        boardCol2: Int,
    ) {
        puzzleService.swapTiles(
            puzzle = this,
            boardRow1 = boardRow1,
            boardCol1 = boardCol1,
            boardRow2 = boardRow2,
            boardCol2 = boardCol2,
        )
    }

    fun rotateTile(
        boardRow: Int,
        boardCol: Int,
    ) {
        puzzleService.rotateTile(
            puzzle = this,
            boardRow = boardRow,
            boardCol = boardCol,
        )
    }

    fun isSolved(): Boolean {
        return puzzleSolver.isSolved(this)
    }

    fun isTileSolved(
        tile: TileModel,
    ): Boolean {
        return puzzleSolver.isTileSolved(
            puzzle = this,
            tile = tile,
        )
    }

    fun getScrambledPicUnitIDAt(row: Int, col: Int): Int {
        return scrambled[row][col]
    }

    fun getSolutionPicUnitIDAt(row: Int, col: Int): Int {
        return solution[row][col]
    }

    val boardRows: Int
        get() = if (tileRows == 0) 0 else rows / tileRows

    val boardCols: Int
        get() = if (tileCols == 0) 0 else cols / tileCols

    fun makeExistingPuzzle(
        scrambled: Array<IntArray>,
        solution: Array<IntArray>,
        tileRows: Int,
        tileCols: Int,
        doRotations: Boolean = false,
        seed: Long? = null
    ) {
        this.scrambled = scrambled
        this.solution = solution
        this.rows = scrambled.size
        this.cols = if (scrambled.isNotEmpty()) scrambled[0].size else 0
        this.tileRows = tileRows
        this.tileCols = tileCols
        this.doRotations = doRotations
        this.seed = seed
    }

    fun makePuzzleWithWalls(
        rows: Int = 0,
        cols: Int = 0,
        tileRows: Int = 0,
        tileCols: Int = 0,
        isFlipOU: Boolean = false,
        isUnify: Boolean = false,
        doRotations: Boolean = false,
        seed: Long? = null,
    ) {

        if (isBadSize(rows, cols, tileRows, tileCols)) return

        this.scrambled = Array(rows) { IntArray(cols) }
        this.solution = Array(rows) { IntArray(cols) }
        this.rows = rows
        this.cols = cols
        this.tileRows = tileRows
        this.tileCols = tileCols
        this.doRotations = doRotations
        this.seed = seed

        // Give at least 10 tries to generate a puzzle that is not already solved
        // after being scrambled. This is rare, but possible for small puzzles.
        var cnt = 0
        do {
            walledPuzzleGenerator.generate(
                puzzle = this,
                isFlipOU = isFlipOU,
            )

            if (isUnify) {
                puzzleService.unifyKnots(this)
                puzzleService.fixOU(this)
            }

            puzzleService.scramble(this)

        } while (puzzleSolver.isSolved(this) && ++cnt < 10)
    }

    suspend fun makePuzzleWithoutWalls(
        rows: Int = 0,
        cols: Int = 0,
        tileRows: Int = 0,
        tileCols: Int = 0,
        isUnify: Boolean = false,
        doRotations: Boolean = false,
        seed: Long? = null,
    ) {
        if (isBadSize(rows, cols, tileRows, tileCols)) return

        this.scrambled = Array(rows) { IntArray(cols) }
        this.solution = Array(rows) { IntArray(cols) }
        this.rows = rows
        this.cols = cols
        this.tileRows = tileRows
        this.tileCols = tileCols
        this.doRotations = doRotations
        this.seed = seed

        // Give at least 10 tries to generate a puzzle that is not already solved
        // after being scrambled. This is rare, but possible for small puzzles.
        var cnt = 0
        do {
            // Not-walled puzzles can fail to generate due to their random nature.
            // If we fail, just fall back to a walled puzzle which always succeeds.
            if (notWalledPuzzleGenerator.generate(this) != KnotMakeCode.Success) {
                walledPuzzleGenerator.generate(this)
            }
            if (isUnify) {
                puzzleService.unifyKnots(this)
            }
            puzzleService.fixOU(this)

            puzzleService.scramble(this)

        } while (puzzleSolver.isSolved(this) && ++cnt < 10)
    }

    private fun isBadSize(
        rows: Int,
        cols: Int,
        tileRows: Int,
        tileCols: Int,
    ): Boolean {
        return rows <= 0 || cols <= 0 || tileRows <= 0 || tileCols <= 0
                || rows % 2 != 0 || cols % 2 != 0
                || rows % tileRows != 0 || cols % tileCols != 0
                || rows < tileRows || cols < tileCols
    }
}