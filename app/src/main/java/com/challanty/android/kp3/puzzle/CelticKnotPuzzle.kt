package com.challanty.android.kp3.puzzle
import com.challanty.android.kp3.viewModel.TileModel
import kotlinx.coroutines.yield

class CelticKnotPuzzle() {
    var scrambled: Array<IntArray> = Array(0) { IntArray(0) }
    var solution: Array<IntArray> = Array(0) { IntArray(0) }
    var rows: Int = 0
    var cols: Int = 0
    var tileRows: Int = 0
    var tileCols: Int = 0
    var isFlipOU: Boolean = false
    var isUnify: Boolean = false
    var doRotations: Boolean = false
    var seed: Long? = null

    private val walledPuzzleGenerator = CelticKnotWalledPuzzleGenerator()
    private val notWalledPuzzleGenerator = CelticKnotNotWalledPuzzleGenerator()
    private val puzzleService = CelticKnotPuzzleService()
    private val puzzleSolver = CelticKnotPuzzleSolver()

    constructor(
        rows: Int = 0,
        cols: Int = 0,
        tileRows: Int = 0,
        tileCols: Int = 0,
        isFlipOU: Boolean = false,
        isUnify: Boolean = false,
        doRotations: Boolean = false,
        isWithWalls: Boolean = true,
        seed: Long? = null,
    ) : this() {
        this.scrambled = Array(rows) { IntArray(cols) }
        this.solution = Array(rows) { IntArray(cols) }
        this.rows = rows
        this.cols = cols
        this.tileRows = tileRows
        this.tileCols = tileCols
        this.isFlipOU = isFlipOU
        this.isUnify = isUnify
        this.doRotations = doRotations
        this.seed = seed
        if (rows > 0 && cols > 0 && tileRows > 0 && tileCols > 0
            && rows % 2 == 0 && cols % 2 == 0
            && rows % tileRows == 0 && cols % tileCols == 0
            && rows >= tileRows && cols >= tileCols
        ) {
            makePuzzle(
                isWithWalls = isWithWalls,
                isUnify = isUnify,
            )
        }
    }

    constructor(
        scrambled: Array<IntArray>,
        solution: Array<IntArray>,
        tileRows: Int,
        tileCols: Int,
        doRotations: Boolean = false,
        seed: Long? = null,
    ) : this() {
        this.scrambled = scrambled
        this.solution = solution
        this.rows = scrambled.size
        this.cols = if (scrambled.isNotEmpty()) scrambled[0].size else 0
        this.tileRows = tileRows
        this.tileCols = tileCols
        this.isFlipOU = false
        this.isUnify = false
        this.doRotations = doRotations
        this.seed = seed
    }

    fun swapTiles(
        boardRow1: Int,
        boardCol1: Int,
        boardRow2: Int,
        boardCol2: Int,
    ) {
        puzzleService.swapTiles(
            puzzle = this,
            boardRow1 = boardRow1,
            boardCol1 = boardCol1,
            boardRow2 = boardRow2,
            boardCol2 = boardCol2,
        )
    }

    fun rotateTile(
        boardRow: Int,
        boardCol: Int,
    ) {
        puzzleService.rotateTile(
            puzzle = this,
            boardRow = boardRow,
            boardCol = boardCol,
        )
    }

    fun isSolved(): Boolean {
        return puzzleSolver.isSolved(this)
    }

    fun isTileSolved(
        tile: TileModel,
    ): Boolean {
        return puzzleSolver.isTileSolved(
            puzzle = this,
            tile = tile,
        )
    }

    fun getScrambledPicUnitIDAt(row: Int, col: Int): Int {
        return scrambled[row][col]
    }

    fun getSolutionPicUnitIDAt(row: Int, col: Int): Int {
        return solution[row][col]
    }

    val boardRows: Int
        get() = if (tileRows == 0) 0 else rows / tileRows

    val boardCols: Int
        get() = if (tileCols == 0) 0 else cols / tileCols

    fun makeExistingPuzzle(
        scrambled: Array<IntArray>,
        solution: Array<IntArray>,
        tileRows: Int,
        tileCols: Int,
        doRotations: Boolean = false,
        seed: Long? = null
    ) {
        this.scrambled = scrambled
        this.solution = solution
        this.rows = scrambled.size
        this.cols = if (scrambled.isNotEmpty()) scrambled[0].size else 0
        this.tileRows = tileRows
        this.tileCols = tileCols
        this.isFlipOU = false
        this.isUnify = false
        this.doRotations = doRotations
        this.seed = seed
    }

    fun makePuzzleWithWalls(
        rows: Int = 0,
        cols: Int = 0,
        tileRows: Int = 0,
        tileCols: Int = 0,
        isFlipOU: Boolean = false,
        isUnify: Boolean = false,
        doRotations: Boolean = false,
        isWithWalls: Boolean = true,
        seed: Long? = null,
        ) {
        this.scrambled = Array(rows) { IntArray(cols) }
        this.solution = Array(rows) { IntArray(cols) }
        this.rows = rows
        this.cols = cols
        this.tileRows = tileRows
        this.tileCols = tileCols
        this.isFlipOU = isFlipOU
        this.isUnify = isUnify
        this.doRotations = doRotations
        this.seed = seed
        if (rows > 0 && cols > 0 && tileRows > 0 && tileCols > 0
            && rows % 2 == 0 && cols % 2 == 0
            && rows % tileRows == 0 && cols % tileCols == 0
            && rows >= tileRows && cols >= tileCols
        ) {
            makePuzzle(
                isWithWalls = isWithWalls,
                isUnify = isUnify,
            )
        }

        makePuzzle(
            isWithWalls = true,
            isUnify = false,
        )
    }

    fun makePuzzle(
        isWithWalls: Boolean = true,
        isUnify: Boolean = false,
    ) {
        // Give at least 10 tries to generate a puzzle that is not already solved
        // after being scrambled. This is rare, but possible for small puzzles.
        var cnt = 0
        do {
            if (isWithWalls) {
                walledPuzzleGenerator.generate(this)
            } else {
                // Not-walled puzzles can fail to generate due to their random nature.
                // If we fail, just fall back to a walled puzzle which always succeeds.
                if (notWalledPuzzleGenerator.generate(this) != KnotMakeCode.Success) {
                    walledPuzzleGenerator.generate(this)
                }
            }
            if (isUnify) {
                puzzleService.unifyKnots(this)
            }
            if (isUnify || !isWithWalls) {
                puzzleService.fixOU(this)
            }

            puzzleService.scramble(this)
            cnt++
        } while (puzzleSolver.isSolved(this) && cnt < 10)
    }
}