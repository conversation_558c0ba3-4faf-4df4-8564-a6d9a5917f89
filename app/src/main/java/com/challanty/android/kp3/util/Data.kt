package com.challanty.android.kp3.util

const val APP_VERSION = 1

const val DEFAULT_BOARD_ROWS = 4
const val DEFAULT_BOARD_COLS = 4
const val DEFAULT_TILE_ROWS = 2
const val DEFAULT_TILE_COLS = 2
const val DEFAULT_LOCK_PERCENT = 100
const val DEFAULT_TILES_ROTATABLE = false
const val DEFAULT_ANIMATE = true
const val DEFAULT_ANIMATE_ROTATION = true
const val DEFAULT_ANIMATE_SWAP = true
const val DEFAULT_SINGLE_KNOT = false

// The default game solution.
// These are picture unit IDs for 2x2 tiles in
// 4 tile rows and 4 tile cols.
val DEFAULT_SOLUTION = arrayOf(
    intArrayOf(20, 9, 7, 9, 7, 9, 7, 21),
    intArrayOf(6, 17, 18, 17, 18, 17, 18, 10),
    intArrayOf(8, 16, 19, 16, 19, 16, 19, 4),
    intArrayOf(6, 17, 18, 17, 18, 17, 18, 10),
    intArrayOf(8, 16, 19, 16, 19, 16, 19, 4),
    intArrayOf(6, 17, 18, 17, 18, 17, 18, 10),
    intArrayOf(8, 16, 19, 16, 19, 16, 19, 4),
    intArrayOf(23, 5, 11, 5, 11, 5, 11, 22)
)

// The default scrambled version of DEFAULT_SOLUTION
val DEFAULT_BOARD = arrayOf(
    intArrayOf(7, 21, 8, 16, 19, 4, 19, 16),
    intArrayOf(18, 10, 6, 17, 18, 10, 18, 17),
    intArrayOf(19, 16, 20, 9, 8, 16, 19, 16),
    intArrayOf(18, 17, 6, 17, 6, 17, 18, 17),
    intArrayOf(7, 9, 19, 4, 19, 4, 19, 16),
    intArrayOf(18, 17, 18, 10, 11, 22, 11, 5),
    intArrayOf(19, 16, 8, 16, 7, 9, 19, 16),
    intArrayOf(18, 17, 23, 5, 18, 17, 11, 5)
)

// The default tile quarter turns
val DEFAULT_TILE_LOCKS = arrayOf(
    intArrayOf(1, 1, 1, 1),
    intArrayOf(1, 1, 1, 1),
    intArrayOf(1, 1, 1, 1),
    intArrayOf(1, 1, 1, 1),
    intArrayOf(1, 1, 1, 1),
    intArrayOf(1, 1, 1, 1),
    intArrayOf(1, 1, 1, 1),
    intArrayOf(1, 1, 1, 1)
)

const val MIN_DIMENSION = 1
const val MAX_DIMENSION = 6

val LIST_OF_ALL_DIMENSIONS = listOf(1, 2, 3, 4, 5, 6)
val LIST_OF_ALL_DIMENSIONS_EXCEPT_1 = listOf(2, 3, 4, 5, 6)
val LIST_OF_EVEN_DIMENSIONS = listOf(2, 4, 6)

const val ANIMATE_SWAP_DURATION = 500
const val ANIMATE_ROTATION_DURATION = 500

const val PIC_UNIT_BOX_SIZE = 100f
const val BG_PIC_UNIT_BOX_SIZE = 40f

const val HEADING_UP = 0
const val HEADING_DOWN = 1
const val HEADING_LEVEL = 2

// IN_2_OUT is a map of maps that defines for each Celtic ID plus an associated entrance,
// an I2Odata data class of values for traversing a knot.
//
// There are 8 possible entrances/exits for a Celtic Knot Letter: along a diagonal
// (positions 1, 3, 5, and 7), along the vertical (8 and 4), or along the
// horizontal (2 and 6):
//
// 1   8   7
//  |-----|
// 2|     |6
//  |-----|
// 3   4   5
//
// For example, the vertical pipe "||" allows you to enter the pipe from 8 and
// then enter the neighboring character from their entrance 8 as well or enter
// the pipe from 4 and enter the neighboring character from their 4 as well.
//
data class I2Odata(
    val nextEnt: Int,           // entrance # to use to get to the next ID
    val heading: Int,           // whether we are over, under or none
    val delta: Pair<Int, Int>,  // row/col delta needed to get to the next ID in the knot grid
)

val IN_2_OUT = mapOf(
    0 to mapOf(
        1 to I2Odata(8, HEADING_DOWN, Pair(1, 0)),
        4 to I2Odata(5, HEADING_UP, Pair(-1, -1))
    ),
    1 to mapOf(
        2 to I2Odata(3, HEADING_UP, Pair(-1, 1)),
        7 to I2Odata(6, HEADING_DOWN, Pair(0, -1))
    ),
    2 to mapOf(
        5 to I2Odata(4, HEADING_DOWN, Pair(-1, 0)),
        8 to I2Odata(1, HEADING_UP, Pair(1, 1))
    ),
    3 to mapOf(
        3 to I2Odata(2, HEADING_DOWN, Pair(0, 1)),
        6 to I2Odata(7, HEADING_UP, Pair(1, -1))
    ),
    4 to mapOf(
        1 to I2Odata(8, HEADING_UP, Pair(1, 0)),
        4 to I2Odata(5, HEADING_DOWN, Pair(-1, -1))
    ),
    5 to mapOf(
        2 to I2Odata(3, HEADING_DOWN, Pair(-1, 1)),
        7 to I2Odata(6, HEADING_UP, Pair(0, -1))
    ),
    6 to mapOf(
        5 to I2Odata(4, HEADING_UP, Pair(-1, 0)),
        8 to I2Odata(1, HEADING_DOWN, Pair(1, 1))
    ),
    7 to mapOf(
        3 to I2Odata(2, HEADING_UP, Pair(0, 1)),
        6 to I2Odata(7, HEADING_DOWN, Pair(1, -1))
    ),
    8 to mapOf(
        4 to I2Odata(3, HEADING_UP, Pair(-1, 1)),
        7 to I2Odata(8, HEADING_DOWN, Pair(1, 0))
    ),
    9 to mapOf(
        2 to I2Odata(1, HEADING_UP, Pair(1, 1)),
        5 to I2Odata(6, HEADING_DOWN, Pair(0, -1))
    ),
    10 to mapOf(
        3 to I2Odata(4, HEADING_DOWN, Pair(-1, 0)),
        8 to I2Odata(7, HEADING_UP, Pair(1, -1))
    ),
    11 to mapOf(
        1 to I2Odata(2, HEADING_DOWN, Pair(0, 1)),
        6 to I2Odata(5, HEADING_UP, Pair(-1, -1))
    ),
    12 to mapOf(
        4 to I2Odata(3, HEADING_DOWN, Pair(-1, 1)),
        7 to I2Odata(8, HEADING_UP, Pair(1, 0))
    ),
    13 to mapOf(
        2 to I2Odata(1, HEADING_DOWN, Pair(1, 1)),
        5 to I2Odata(6, HEADING_UP, Pair(0, -1))
    ),
    14 to mapOf(
        3 to I2Odata(4, HEADING_UP, Pair(-1, 0)),
        8 to I2Odata(7, HEADING_DOWN, Pair(1, -1))
    ),
    15 to mapOf(
        1 to I2Odata(2, HEADING_UP, Pair(0, 1)),
        6 to I2Odata(5, HEADING_DOWN, Pair(-1, -1))
    ),
    16 to mapOf(
        1 to I2Odata(1, HEADING_UP, Pair(1, 1)),
        5 to I2Odata(5, HEADING_DOWN, Pair(-1, -1))
    ),
    17 to mapOf(
        3 to I2Odata(3, HEADING_DOWN, Pair(-1, 1)),
        7 to I2Odata(7, HEADING_UP, Pair(1, -1))
    ),
    18 to mapOf(
        1 to I2Odata(1, HEADING_DOWN, Pair(1, 1)),
        5 to I2Odata(5, HEADING_UP, Pair(-1, -1))
    ),
    19 to mapOf(
        3 to I2Odata(3, HEADING_UP, Pair(-1, 1)),
        7 to I2Odata(7, HEADING_DOWN, Pair(1, -1))
    ),
    20 to mapOf(
        4 to I2Odata(2, HEADING_LEVEL, Pair(0, 1)),
        6 to I2Odata(8, HEADING_LEVEL, Pair(1, 0))
    ),
    21 to mapOf(
        2 to I2Odata(8, HEADING_LEVEL, Pair(1, 0)),
        4 to I2Odata(6, HEADING_LEVEL, Pair(0, -1))
    ),
    22 to mapOf(
        2 to I2Odata(4, HEADING_LEVEL, Pair(-1, 0)),
        8 to I2Odata(6, HEADING_LEVEL, Pair(0, -1))
    ),
    23 to mapOf(
        6 to I2Odata(4, HEADING_LEVEL, Pair(-1, 0)),
        8 to I2Odata(2, HEADING_LEVEL, Pair(0, 1))
    ),
    24 to mapOf(
        4 to I2Odata(4, HEADING_LEVEL, Pair(-1, 0)),
        8 to I2Odata(8, HEADING_LEVEL, Pair(1, 0))
    ),
    25 to mapOf(
        2 to I2Odata(2, HEADING_LEVEL, Pair(0, 1)),
        6 to I2Odata(6, HEADING_LEVEL, Pair(0, -1))
    )
)

// FLIP_OU[x] == Y if Y is the same oic unit as x, but the over/under is flipped,
// if x has no over/under flip, then flipOU[x] == x
val FLIP_OU = mapOf(
    0 to 4,
    1 to 5,
    2 to 6,
    3 to 7,
    4 to 0,
    5 to 1,
    6 to 2,
    7 to 3,
    8 to 12,
    9 to 13,
    10 to 14,
    11 to 15,
    12 to 8,
    13 to 9,
    14 to 10,
    15 to 11,
    16 to 18,
    17 to 19,
    18 to 16,
    19 to 17,
    20 to 20,
    21 to 21,
    22 to 22,
    23 to 23,
    24 to 24,
    25 to 25
)

const val BORDER_ID = 26

val RHT_N = mutableMapOf(
    0 to listOf(2, 6, 8, 12, 20, 23, 24),
    1 to listOf(4, 15, 16),
    2 to listOf(7, 14, 19),
    3 to listOf(5, 13, 21, 22, 25),
    4 to listOf(2, 6, 8, 12, 20, 23, 24),
    5 to listOf(0, 11, 18),
    6 to listOf(3, 10, 17),
    7 to listOf(1, 9, 21, 22, 25),
    8 to listOf(4, 15, 16),
    9 to listOf(7, 14, 19),
    10 to listOf(2, 6, 8, 12, 20, 23, 24),
    11 to listOf(5, 13, 21, 22, 25),
    12 to listOf(0, 11, 18),
    13 to listOf(3, 10, 17),
    14 to listOf(2, 6, 8, 12, 20, 23, 24),
    15 to listOf(1, 9, 21, 22, 25),
    16 to listOf(7, 14, 19),
    17 to listOf(0, 11, 18),
    18 to listOf(3, 10, 17),
    19 to listOf(4, 15, 16),
    20 to listOf(1, 5, 9, 13, 21, 22, 25),
    21 to listOf(2, 6, 8, 12, 20, 23, 24),
    22 to listOf(2, 6, 8, 12, 20, 23, 24),
    23 to listOf(1, 5, 9, 13, 21, 22, 25),
    24 to listOf(2, 6, 8, 12, 20, 23, 24),
    25 to listOf(1, 5, 9, 13, 21, 22, 25),
    BORDER_ID to listOf(2, 6, 8, 12, 20, 23, 24)
)

/**
 * BOT_N ("bottom neighbor") maps all Celtic Knot Picture Unit IDs (PUs) plus the BORDER_ID to
 * the list all PUs that can connect to the bottom of the PU.
 * NOTE: This is currently only used to create IS_BOT_N.
 */
val BOT_N = mutableMapOf(
    0 to listOf(6, 14, 22, 23, 24),
    1 to listOf(3, 7, 9, 13, 20, 21, 25),
    2 to listOf(5, 12, 17),
    3 to listOf(4, 15, 16),
    4 to listOf(2, 10, 22, 23, 24),
    5 to listOf(3, 7, 9, 13, 20, 21, 25),
    6 to listOf(1, 8, 19),
    7 to listOf(0, 11, 18),
    8 to listOf(6, 14, 22, 23, 24),
    9 to listOf(5, 12, 17),
    10 to listOf(4, 15, 16),
    11 to listOf(3, 7, 9, 13, 20, 21, 25),
    12 to listOf(2, 10, 22, 23, 24),
    13 to listOf(1, 8, 19),
    14 to listOf(0, 11, 18),
    15 to listOf(3, 7, 9, 13, 20, 21, 25),
    16 to listOf(5, 12, 17),
    17 to listOf(4, 15, 16),
    18 to listOf(1, 8, 19),
    19 to listOf(0, 11, 18),
    20 to listOf(2, 6, 10, 14, 22, 23, 24),
    21 to listOf(2, 6, 10, 14, 22, 23, 24),
    22 to listOf(3, 7, 9, 13, 20, 21, 25),
    23 to listOf(3, 7, 9, 13, 20, 21, 25),
    24 to listOf(2, 6, 10, 14, 22, 23, 24),
    25 to listOf(3, 7, 9, 13, 20, 21, 25),
    BORDER_ID to listOf(3, 7, 9, 13, 20, 21, 25)
)

/**
 * IS_RHT_N and IS_BOT_N are used to determine whether a PU ID can connect to the right or bottom
 * of a specified PU.
 */
val IS_RHT_N = mutableMapOf<Int, MutableMap<Int, Boolean>>()
val IS_BOT_N = mutableMapOf<Int, MutableMap<Int, Boolean>>()

// H_MELDS = Map<a, Map<b, Map<c, Map<d, idx>>>> where
//     a and b are an adjacent pair of IDs from one knot and
//     c and d are an adjacent pair of IDs from another knot that
//     is either above or below the first knot.
//     idx = idx of array in horizontalMeldData that lists the
//           IDs to substitute for a, b, c, and d to make the meld.
// By switching the "roles" played by a, b, c, and d, this data can be used
// for both top-to-bottom and bottom-to-top melds as well as left-to-right
// and right-to-left traversals. The default assumption is that a top-to-bottom
// meld is being made while traversing left-to-right. Thus, a and b are
// contained in the "from" knot and c and d are contained in the "to" knot.
// Also, the default assumption is that a is the previous ID and b is the current ID
// and similarly for c and d. If the data doesn't match with these assumptions, then we can
// swap the roles of a and b and assume they are contained in the "to" knot. Or we can
// swap the order of a and b and assume a is the current ID and b is the previous ID.
// The same applies to c and d. Whatever rolls a, b, c, and d are used for in a meld, the
// e, f, g, and h IDs from the horizontalMeldData must play the same roles.
val H_MELDS = mapOf(
    11 to mapOf(
        5 to mapOf(
            3 to mapOf(13 to 0, 21 to 1, 25 to 2),
            7 to mapOf(9 to 0, 21 to 1, 25 to 2),
            20 to mapOf(9 to 3, 13 to 3, 21 to 4, 25 to 5),
            25 to mapOf(9 to 6, 13 to 6, 21 to 7, 25 to 8),
        ),
        22 to mapOf(
            3 to mapOf(13 to 9, 21 to 10, 25 to 11),
            7 to mapOf(9 to 9, 21 to 10, 25 to 11),
            20 to mapOf(9 to 12, 13 to 12, 21 to 27, 25 to 14),
            25 to mapOf(9 to 15, 13 to 15, 21 to 16, 25 to 17),
        ),
        25 to mapOf(
            3 to mapOf(13 to 18, 21 to 19, 25 to 20),
            7 to mapOf(9 to 18, 21 to 19, 25 to 20),
            20 to mapOf(9 to 21, 13 to 21, 21 to 22, 25 to 23),
            25 to mapOf(9 to 24, 13 to 24, 21 to 25, 25 to 26),
        ),
    ),
    15 to mapOf(
        1 to mapOf(
            3 to mapOf(13 to 0, 21 to 1, 25 to 2),
            7 to mapOf(9 to 0, 21 to 1, 25 to 2),
            20 to mapOf(9 to 3, 13 to 3, 21 to 4, 25 to 5),
            25 to mapOf(9 to 6, 13 to 6, 21 to 7, 25 to 8),
        ),
        22 to mapOf(
            3 to mapOf(13 to 9, 21 to 10, 25 to 11),
            7 to mapOf(9 to 9, 21 to 10, 25 to 11),
            20 to mapOf(9 to 12, 13 to 12, 21 to 13, 25 to 14),
            25 to mapOf(9 to 15, 13 to 15, 21 to 16, 25 to 17),
        ),
        25 to mapOf(
            3 to mapOf(13 to 18, 21 to 19, 25 to 20),
            7 to mapOf(9 to 18, 21 to 19, 25 to 20),
            20 to mapOf(9 to 21, 13 to 21, 21 to 22, 25 to 23),
            25 to mapOf(9 to 24, 13 to 24, 21 to 25, 25 to 26),
        ),
    ),
    23 to mapOf(
        1 to mapOf(
            3 to mapOf(13 to 28, 21 to 29, 25 to 30),
            7 to mapOf(9 to 28, 21 to 29, 25 to 30),
            20 to mapOf(9 to 31, 13 to 31, 21 to 32, 25 to 33),
            25 to mapOf(9 to 34, 13 to 34, 21 to 35, 25 to 36),
        ),
        5 to mapOf(
            3 to mapOf(13 to 28, 21 to 29, 25 to 30),
            7 to mapOf(9 to 28, 21 to 29, 25 to 30),
            20 to mapOf(9 to 31, 13 to 31, 21 to 32, 25 to 33),
            25 to mapOf(9 to 34, 13 to 34, 21 to 35, 25 to 36),
        ),
        22 to mapOf(
            3 to mapOf(13 to 37, 21 to 38, 25 to 39),
            7 to mapOf(9 to 37, 21 to 38, 25 to 39),
            20 to mapOf(9 to 40, 13 to 40, 21 to 41, 25 to 42),
            25 to mapOf(9 to 43, 13 to 43, 21 to 44, 25 to 45),
        ),
        25 to mapOf(
            3 to mapOf(13 to 46, 21 to 47, 25 to 48),
            7 to mapOf(9 to 46, 21 to 47, 25 to 48),
            20 to mapOf(9 to 49, 13 to 49, 21 to 50, 25 to 51),
            25 to mapOf(9 to 52, 13 to 52, 21 to 53, 25 to 54),
        ),
    ),
    25 to mapOf(
        1 to mapOf(
            3 to mapOf(13 to 55, 21 to 56, 25 to 57),
            7 to mapOf(9 to 55, 21 to 56, 25 to 57),
            20 to mapOf(9 to 58, 13 to 58, 21 to 59, 25 to 60),
            25 to mapOf(9 to 61, 13 to 61, 21 to 62, 25 to 63),
        ),
        5 to mapOf(
            3 to mapOf(13 to 55, 21 to 56, 25 to 57),
            7 to mapOf(9 to 55, 21 to 56, 25 to 57),
            20 to mapOf(9 to 58, 13 to 58, 21 to 59, 25 to 60),
            25 to mapOf(9 to 61, 13 to 61, 21 to 62, 25 to 63),
        ),
        22 to mapOf(
            3 to mapOf(13 to 64, 21 to 65, 25 to 66),
            7 to mapOf(9 to 64, 21 to 65, 25 to 66),
            20 to mapOf(9 to 67, 13 to 67, 21 to 68, 25 to 69),
            25 to mapOf(9 to 70, 13 to 70, 21 to 71, 25 to 72),
        ),
        25 to mapOf(
            3 to mapOf(13 to 73, 21 to 74, 25 to 75),
            7 to mapOf(9 to 73, 21 to 74, 25 to 75),
            20 to mapOf(9 to 76, 13 to 76, 21 to 77, 25 to 78),
            25 to mapOf(9 to 79, 13 to 79, 21 to 80, 25 to 81),
        ),
    ),
)

// H_MELDS_DATA[idx] = (e, f, g, h) where
// idx comes from horizontalMelds and e is the
// substitute for whatever role a plays in a meld,
// f substitutes for b, g for c, and h for d.
// NOTE: DO NOT CHANGE THE ORDER OF THE ARRAYS
val H_MELDS_DATA = arrayOf(
    arrayOf(0, 8, 10, 2),
    arrayOf(0, 8, 10, 24),
    arrayOf(0, 8, 10, 23),
    arrayOf(0, 8, 24, 2),
    arrayOf(0, 8, 24, 24),
    arrayOf(0, 8, 24, 23),
    arrayOf(0, 8, 22, 2),
    arrayOf(0, 8, 22, 24),
    arrayOf(0, 8, 22, 23),
    arrayOf(0, 24, 10, 2),
    arrayOf(0, 24, 10, 24),
    arrayOf(0, 24, 10, 23),
    arrayOf(0, 24, 24, 2),
    arrayOf(0, 24, 24, 24),
    arrayOf(0, 24, 24, 23),
    arrayOf(0, 24, 22, 2),
    arrayOf(0, 24, 22, 24),
    arrayOf(0, 24, 22, 23),
    arrayOf(0, 20, 10, 2),
    arrayOf(0, 20, 10, 24),
    arrayOf(0, 20, 10, 23),
    arrayOf(0, 20, 24, 2),
    arrayOf(0, 20, 24, 24),
    arrayOf(0, 20, 24, 23),
    arrayOf(0, 20, 22, 2),
    arrayOf(0, 20, 22, 24),
    arrayOf(0, 20, 22, 23),
    arrayOf(0, 24, 24, 24),
    arrayOf(24, 8, 10, 2),
    arrayOf(24, 8, 10, 24),
    arrayOf(24, 8, 10, 23),
    arrayOf(24, 8, 24, 2),
    arrayOf(24, 8, 24, 24),
    arrayOf(24, 8, 24, 23),
    arrayOf(24, 8, 22, 2),
    arrayOf(24, 8, 22, 24),
    arrayOf(24, 8, 22, 23),
    arrayOf(24, 24, 10, 2),
    arrayOf(24, 24, 10, 24),
    arrayOf(24, 24, 10, 23),
    arrayOf(24, 24, 24, 2),
    arrayOf(24, 24, 24, 24),
    arrayOf(24, 24, 24, 23),
    arrayOf(24, 24, 22, 2),
    arrayOf(24, 24, 22, 24),
    arrayOf(24, 24, 22, 23),
    arrayOf(24, 20, 10, 2),
    arrayOf(24, 20, 10, 24),
    arrayOf(24, 20, 10, 23),
    arrayOf(24, 20, 24, 2),
    arrayOf(24, 20, 24, 24),
    arrayOf(24, 20, 24, 23),
    arrayOf(24, 20, 22, 2),
    arrayOf(24, 20, 22, 24),
    arrayOf(24, 20, 22, 23),
    arrayOf(21, 8, 10, 2),
    arrayOf(21, 8, 10, 24),
    arrayOf(21, 8, 10, 23),
    arrayOf(21, 8, 24, 2),
    arrayOf(21, 8, 24, 24),
    arrayOf(21, 8, 24, 23),
    arrayOf(21, 8, 22, 2),
    arrayOf(21, 8, 22, 24),
    arrayOf(21, 8, 22, 23),
    arrayOf(21, 24, 10, 2),
    arrayOf(21, 24, 10, 24),
    arrayOf(21, 24, 10, 23),
    arrayOf(21, 24, 24, 2),
    arrayOf(21, 24, 24, 24),
    arrayOf(21, 24, 24, 23),
    arrayOf(21, 24, 22, 2),
    arrayOf(21, 24, 22, 24),
    arrayOf(21, 24, 22, 23),
    arrayOf(21, 20, 10, 2),
    arrayOf(21, 20, 10, 24),
    arrayOf(21, 20, 10, 23),
    arrayOf(21, 20, 24, 2),
    arrayOf(21, 20, 24, 24),
    arrayOf(21, 20, 24, 23),
    arrayOf(21, 20, 22, 2),
    arrayOf(21, 20, 22, 24),
    arrayOf(21, 20, 22, 23),
)

// V_MELDS is the same as H_MELDS, except that
// the two knots are to the left and right of each other and
// the traversal can be going up or down.
// The default assumption is that a right-to-left meld is
// being done while traversing down.
val V_MELDS = mapOf(
    8 to mapOf(
        6 to mapOf(
            0 to mapOf(14 to 0, 22 to 1, 24 to 2),
            4 to mapOf(10 to 0, 22 to 1, 24 to 2),
            21 to mapOf(10 to 5, 14 to 5, 22 to 3, 24 to 4),
            24 to mapOf(10 to 7, 14 to 7, 22 to 8, 24 to 6),
        ),
        23 to mapOf(
            0 to mapOf(14 to 9, 22 to 10, 24 to 11),
            4 to mapOf(10 to 9, 22 to 10, 24 to 11),
            21 to mapOf(10 to 12, 14 to 12, 22 to 13, 24 to 14),
            24 to mapOf(10 to 15, 14 to 15, 22 to 16, 24 to 17),
        ),
        24 to mapOf(
            0 to mapOf(14 to 18, 22 to 19, 24 to 20),
            4 to mapOf(10 to 18, 22 to 19, 24 to 20),
            21 to mapOf(10 to 21, 14 to 21, 22 to 22, 24 to 23),
            24 to mapOf(10 to 24, 14 to 24, 22 to 25, 24 to 26),
        ),
    ),
    12 to mapOf(
        2 to mapOf(
            0 to mapOf(14 to 0, 22 to 1, 24 to 2),
            4 to mapOf(10 to 0, 22 to 1, 24 to 2),
            21 to mapOf(10 to 5, 14 to 5, 22 to 3, 24 to 4),
            24 to mapOf(10 to 7, 14 to 7, 22 to 8, 24 to 6),
        ),
        23 to mapOf(
            0 to mapOf(14 to 9, 22 to 10, 24 to 11),
            4 to mapOf(10 to 9, 22 to 10, 24 to 11),
            21 to mapOf(10 to 12, 14 to 12, 22 to 13, 24 to 14),
            24 to mapOf(10 to 15, 14 to 15, 22 to 16, 24 to 17),
        ),
        24 to mapOf(
            0 to mapOf(14 to 18, 22 to 19, 24 to 20),
            4 to mapOf(10 to 18, 22 to 19, 24 to 20),
            21 to mapOf(10 to 21, 14 to 21, 22 to 22, 24 to 23),
            24 to mapOf(10 to 24, 14 to 24, 22 to 25, 24 to 26),
        ),
    ),
    20 to mapOf(
        2 to mapOf(
            0 to mapOf(14 to 27, 22 to 28, 24 to 29),
            4 to mapOf(10 to 27, 22 to 28, 24 to 29),
            21 to mapOf(10 to 30, 14 to 30, 22 to 31, 24 to 32),
            24 to mapOf(10 to 33, 14 to 33, 22 to 34, 24 to 35),
        ),
        6 to mapOf(
            0 to mapOf(14 to 27, 22 to 28, 24 to 29),
            4 to mapOf(10 to 27, 22 to 28, 24 to 29),
            21 to mapOf(10 to 30, 14 to 30, 22 to 31, 24 to 32),
            24 to mapOf(10 to 33, 14 to 33, 22 to 34, 24 to 35),
        ),
        23 to mapOf(
            0 to mapOf(14 to 36, 22 to 37, 24 to 38),
            4 to mapOf(10 to 36, 22 to 37, 24 to 38),
            21 to mapOf(10 to 39, 14 to 39, 22 to 40, 24 to 41),
            24 to mapOf(10 to 42, 14 to 42, 22 to 43, 24 to 44),
        ),
        24 to mapOf(
            0 to mapOf(14 to 45, 22 to 46, 24 to 47),
            4 to mapOf(10 to 45, 22 to 46, 24 to 47),
            21 to mapOf(10 to 48, 14 to 48, 22 to 49, 24 to 50),
            24 to mapOf(10 to 51, 14 to 51, 22 to 52, 24 to 53),
        ),
    ),
    24 to mapOf(
        2 to mapOf(
            0 to mapOf(14 to 54, 22 to 55, 24 to 56),
            4 to mapOf(10 to 54, 22 to 55, 24 to 56),
            21 to mapOf(10 to 57, 14 to 57, 22 to 58, 24 to 59),
            24 to mapOf(10 to 60, 14 to 60, 22 to 61, 24 to 62),
        ),
        6 to mapOf(
            0 to mapOf(14 to 54, 22 to 55, 24 to 56),
            4 to mapOf(10 to 54, 22 to 55, 24 to 56),
            21 to mapOf(10 to 57, 14 to 57, 22 to 58, 24 to 59),
            24 to mapOf(10 to 60, 14 to 60, 22 to 61, 24 to 62),
        ),
        23 to mapOf(
            0 to mapOf(14 to 63, 22 to 64, 24 to 65),
            4 to mapOf(10 to 63, 22 to 64, 24 to 65),
            21 to mapOf(10 to 66, 14 to 66, 22 to 67, 24 to 68),
            24 to mapOf(10 to 69, 14 to 69, 22 to 70, 24 to 71),
        ),
        24 to mapOf(
            0 to mapOf(14 to 72, 22 to 73, 24 to 74),
            4 to mapOf(10 to 72, 22 to 73, 24 to 74),
            21 to mapOf(10 to 75, 14 to 75, 22 to 76, 24 to 77),
            24 to mapOf(10 to 78, 14 to 78, 22 to 79, 24 to 80),
        ),
    ),
)

// V_MELDS_DATA[idx] is the same as H_MELDS_DATA[idx], except that
// the two knots are to the left and right of each other
// NOTE: DO NOT CHANGE THE ORDER OF THE ARRAYS
val V_MELDS_DATA = arrayOf(
    arrayOf(1, 9, 11, 3),
    arrayOf(1, 9, 11, 25),
    arrayOf(1, 9, 11, 20),
    arrayOf(1, 9, 25, 25),
    arrayOf(1, 9, 25, 20),
    arrayOf(1, 9, 25, 3),
    arrayOf(1, 9, 23, 20),
    arrayOf(1, 9, 23, 3),
    arrayOf(1, 9, 23, 25),
    arrayOf(1, 25, 11, 3),
    arrayOf(1, 25, 11, 25),
    arrayOf(1, 25, 11, 20),
    arrayOf(1, 25, 25, 3),
    arrayOf(1, 25, 25, 25),
    arrayOf(1, 25, 25, 20),
    arrayOf(1, 25, 23, 3),
    arrayOf(1, 25, 23, 25),
    arrayOf(1, 25, 23, 20),
    arrayOf(1, 21, 11, 3),
    arrayOf(1, 21, 11, 25),
    arrayOf(1, 21, 11, 20),
    arrayOf(1, 21, 25, 3),
    arrayOf(1, 21, 25, 25),
    arrayOf(1, 21, 25, 20),
    arrayOf(1, 21, 23, 3),
    arrayOf(1, 21, 23, 25),
    arrayOf(1, 21, 23, 20),
    arrayOf(25, 9, 11, 3),
    arrayOf(25, 9, 11, 25),
    arrayOf(25, 9, 11, 20),
    arrayOf(25, 9, 25, 3),
    arrayOf(25, 9, 25, 25),
    arrayOf(25, 9, 25, 20),
    arrayOf(25, 9, 23, 3),
    arrayOf(25, 9, 23, 25),
    arrayOf(25, 9, 23, 20),
    arrayOf(25, 25, 11, 3),
    arrayOf(25, 25, 11, 25),
    arrayOf(25, 25, 11, 20),
    arrayOf(25, 25, 25, 3),
    arrayOf(25, 25, 25, 25),
    arrayOf(25, 25, 25, 20),
    arrayOf(25, 25, 23, 3),
    arrayOf(25, 25, 23, 25),
    arrayOf(25, 25, 23, 20),
    arrayOf(25, 21, 11, 3),
    arrayOf(25, 21, 11, 25),
    arrayOf(25, 21, 11, 20),
    arrayOf(25, 21, 25, 3),
    arrayOf(25, 21, 25, 25),
    arrayOf(25, 21, 25, 20),
    arrayOf(25, 21, 23, 3),
    arrayOf(25, 21, 23, 25),
    arrayOf(25, 21, 23, 20),
    arrayOf(22, 9, 11, 3),
    arrayOf(22, 9, 11, 25),
    arrayOf(22, 9, 11, 20),
    arrayOf(22, 9, 25, 3),
    arrayOf(22, 9, 25, 25),
    arrayOf(22, 9, 25, 20),
    arrayOf(22, 9, 23, 3),
    arrayOf(22, 9, 23, 25),
    arrayOf(22, 9, 23, 20),
    arrayOf(22, 25, 11, 3),
    arrayOf(22, 25, 11, 25),
    arrayOf(22, 25, 11, 20),
    arrayOf(22, 25, 25, 3),
    arrayOf(22, 25, 25, 25),
    arrayOf(22, 25, 25, 20),
    arrayOf(22, 25, 23, 3),
    arrayOf(22, 25, 23, 25),
    arrayOf(22, 25, 23, 20),
    arrayOf(22, 21, 11, 3),
    arrayOf(22, 21, 11, 25),
    arrayOf(22, 21, 11, 20),
    arrayOf(22, 21, 25, 3),
    arrayOf(22, 21, 25, 25),
    arrayOf(22, 21, 25, 20),
    arrayOf(22, 21, 23, 3),
    arrayOf(22, 21, 23, 25),
    arrayOf(22, 21, 23, 20),
)

const val NO_WALLS = 0b0000
const val TOP_WALL = 0b0001
const val RHT_WALL = 0b0010
const val BOT_WALL = 0b0100
const val LFT_WALL = 0b1000

// Convenience combinations
const val TOP_LFT_COR = TOP_WALL + LFT_WALL
const val TOP_RHT_COR = TOP_WALL + RHT_WALL
const val BOT_LFT_COR = BOT_WALL + LFT_WALL
const val BOT_RHT_COR = BOT_WALL + RHT_WALL
const val V_HALLWAY = LFT_WALL + RHT_WALL
const val H_HALLWAY = TOP_WALL + BOT_WALL

private const val MAX_WALL_IDX = BOT_LFT_COR

// Lookup table for pattern IDs based on row and column parity (even/odd)
// plus wall configuration.
val WALLS_2_ID = Array(2) { Array(2) { IntArray(MAX_WALL_IDX + 1) } }
