package com.challanty.android.kp3.puzzle

import com.challanty.android.kp3.util.BORDER_ID
import com.challanty.android.kp3.util.H_MELDS
import com.challanty.android.kp3.util.H_MELDS_DATA
import com.challanty.android.kp3.util.IN_2_OUT
import com.challanty.android.kp3.util.V_MELDS
import com.challanty.android.kp3.util.V_MELDS_DATA

class CelticKnotPuzzleUnifier {

    fun unifyKnots(
        puzzle: CelticKnotPuzzle
    ) {
        val fromGrid = puzzle.solution
        val toGrid = puzzle.scrambled
        val rows = puzzle.rows
        val cols = puzzle.cols

        var knotCount = 0
        var meldCount = 0

        while (true) {
            var found = false

            for (row in 0 until rows) {
                for (col in 0 until cols) {
                    if (fromGrid[row][col] != BORDER_ID) {
                        if (meldKnots(
                                rows = rows,
                                cols = cols,
                                fromGrid = fromGrid,
                                toGrid = toGrid,
                                startRow = row,
                                startCol = col,
                                knotCount = knotCount,
                            )
                        ) ++meldCount
                        found = true
                        break
                    }
                }
                if (found) {
                    break
                }
            }

            if (!found) {
                break
            }
            ++knotCount
        }

        // Copy melded knot(s) into final knot(s)
        for (row in 0 until rows) {
            for (col in 0 until cols) {
                fromGrid[row][col] = toGrid[row][col]
            }
        }
    }

    private fun meldKnots(
        rows: Int,
        cols: Int,
        fromGrid: Array<IntArray>,
        toGrid: Array<IntArray>,
        startRow: Int,
        startCol: Int,
        knotCount: Int,
    ): Boolean {
        val transferGrid = Array(rows) { IntArray(cols) { BORDER_ID } }
        val changesGrid = Array(rows) { IntArray(cols) { BORDER_ID } }

        var row = startRow
        var col = startCol
        var prevRow = 0   // Placeholder value
        var prevCol = 0   // Placeholder value
        var prevID = 0    // Placeholder value
        var prevEnt: Int

        // Traverse the knot.  Stop when we get to the next char after the current char.
        var curID = fromGrid[row][col]
        var ent = IN_2_OUT[curID]!!.keys.first()

        val stopRow = row + IN_2_OUT[curID]!![ent]!!.delta.first
        val stopCol = col + IN_2_OUT[curID]!![ent]!!.delta.second

        // Save first ID since it needs to be used in the
        // last knot ID pair but will have been removed
        // when it gets used in the first knot ID pair.
        val firstID = fromGrid[row][col]

        var isTraversalStart = true

        // The first knot is always considered melded by default since there's nothing yet to meld to.
        var isMelded = knotCount == 0

        while (true) {
            // Stop if we've looped all the way around a knot.
            // Note: We must make sure we aren't simply encountering the
            // stopping position as we start the knot traversal.
            if ((row == stopRow) && (col == stopCol)) {
                if (isTraversalStart) {
                    isTraversalStart = false
                } else {
                    break
                }
            }

            curID = if ((row == startRow) && (col == startCol)) {
                firstID
            } else {
                fromGrid[row][col]
            }

            // Once melded, the rest of the knot is simply put into transferGrid.
            if (isMelded) {
                transferGrid[row][col] = curID
                fromGrid[row][col] = BORDER_ID
            } else if (!isTraversalStart) {
                val rowDelta = row - prevRow
                val colDelta = col - prevCol

                isMelded = if (rowDelta == 0) {
                    tryHorizontalMeld(
                        rows = rows,
                        fromGrid = fromGrid,
                        toGrid = toGrid,
                        transferGrid = transferGrid,
                        changesGrid = changesGrid,
                        prevID = prevID,
                        curID = curID,
                        prevRow = prevRow,
                        prevCol = prevCol,
                        row = row,
                        col = col,
                    )
                } else if (colDelta == 0) {
                    tryVerticalMeld(
                        cols = cols,
                        fromGrid = fromGrid,
                        toGrid = toGrid,
                        transferGrid = transferGrid,
                        changesGrid = changesGrid,
                        prevID = prevID,
                        curID = curID,
                        prevRow = prevRow,
                        prevCol = prevCol,
                        row = row,
                        col = col,
                    )
                } else false
            }

            // Non-melded IDs go to the transferGrid.
            if (!isMelded) {
                transferGrid[row][col] = curID
                fromGrid[row][col] = BORDER_ID
            }

            // Go to next square (we may be going forward, backward, up, or down)
            prevEnt = ent
            prevRow = row
            prevCol = col
            prevID = curID

            ent = IN_2_OUT[prevID]!![prevEnt]!!.nextEnt
            row += IN_2_OUT[prevID]!![prevEnt]!!.delta.first
            col += IN_2_OUT[prevID]!![prevEnt]!!.delta.second
        }

        // Transfer knot to toGrid
        for (aRow in 0 until rows) {
            for (aCol in 0 until cols) {
                if (transferGrid[aRow][aCol] != BORDER_ID) {
                    toGrid[aRow][aCol] = transferGrid[aRow][aCol]

                    // Restore any clobbered change
                    if (changesGrid[aRow][aCol] != BORDER_ID) {
                        toGrid[aRow][aCol] = changesGrid[aRow][aCol]
                    }
                }
            }
        }

        return isMelded
    }

    private fun tryVerticalMeld(
        cols: Int,
        fromGrid: Array<IntArray>,
        toGrid: Array<IntArray>,
        transferGrid: Array<IntArray>,
        changesGrid: Array<IntArray>,
        prevID: Int,
        curID: Int,
        prevRow: Int,
        prevCol: Int,
        row: Int,
        col: Int,
    ): Boolean {
        var deltaCol: Int
        var fromPrev: Int
        var fromCur: Int
        var toPrev: Int
        var toCur: Int
        var isMelded = false

        for (element1 in V_MELDS) {
            for (element2 in element1.value) {
                for (element3 in element2.value) {
                    for (element4 in element3.value) {
                        if ((col > 0) &&
                            (element1.key == prevID) &&
                            (element2.key == curID) &&
                            (element3.key == toGrid[prevRow][prevCol - 1]) &&
                            (element4.key == toGrid[row][col - 1])
                        ) {
                            // R2L meld while traversing down.
                            // NOTE: This check must be first or this case
                            // won't work: y|y
                            //             y|y
                            deltaCol = -1
                            fromPrev = V_MELDS_DATA[element4.value][0]
                            fromCur = V_MELDS_DATA[element4.value][1]
                            toPrev = V_MELDS_DATA[element4.value][2]
                            toCur = V_MELDS_DATA[element4.value][3]
                        } else if ((col > 0) &&
                            (element1.key == curID) &&
                            (element2.key == prevID) &&
                            (element3.key == toGrid[row][col - 1]) &&
                            (element4.key == toGrid[prevRow][prevCol - 1])
                        ) {
                            // R2L meld while traversing up.
                            deltaCol = -1
                            fromPrev = V_MELDS_DATA[element4.value][1]
                            fromCur = V_MELDS_DATA[element4.value][0]
                            toPrev = V_MELDS_DATA[element4.value][3]
                            toCur = V_MELDS_DATA[element4.value][2]
                        } else if ((col < cols - 1) &&
                            (element1.key == toGrid[prevRow][prevCol + 1]) &&
                            (element2.key == toGrid[row][col + 1]) &&
                            (element3.key == prevID) &&
                            (element4.key == curID)
                        ) {
                            // L2R meld while traversing down.
                            deltaCol = 1
                            fromPrev = V_MELDS_DATA[element4.value][2]
                            fromCur = V_MELDS_DATA[element4.value][3]
                            toPrev = V_MELDS_DATA[element4.value][0]
                            toCur = V_MELDS_DATA[element4.value][1]
                        } else if ((col < cols - 1) &&
                            (element1.key == toGrid[row][col + 1]) &&
                            (element2.key == toGrid[prevRow][prevCol + 1]) &&
                            (element3.key == curID) &&
                            (element4.key == prevID)
                        ) {
                            // From-left-to-right meld while traversing up.
                            deltaCol = 1
                            fromPrev = V_MELDS_DATA[element4.value][3]
                            fromCur = V_MELDS_DATA[element4.value][2]
                            toPrev = V_MELDS_DATA[element4.value][1]
                            toCur = V_MELDS_DATA[element4.value][0]
                        } else continue

                        // Meld the knots
                        transferGrid[prevRow][prevCol] = fromPrev
                        transferGrid[row][col] = fromCur
                        transferGrid[prevRow][prevCol + deltaCol] = toPrev
                        transferGrid[row][col + deltaCol] = toCur

                        // Mark changes
                        changesGrid[prevRow][prevCol] = fromPrev
                        changesGrid[row][col] = fromCur
                        changesGrid[prevRow][prevCol + deltaCol] = toPrev
                        changesGrid[row][col + deltaCol] = toCur

                        // Delete old IDs
                        fromGrid[prevRow][prevCol] = BORDER_ID
                        fromGrid[row][col] = BORDER_ID

                        isMelded = true
                        break
                    }
                    if (isMelded) break
                }
                if (isMelded) break
            }
            if (isMelded) break
        }

        return isMelded
    }

    private fun tryHorizontalMeld(
        rows: Int,
        fromGrid: Array<IntArray>,
        toGrid: Array<IntArray>,
        transferGrid: Array<IntArray>,
        changesGrid: Array<IntArray>,
        prevID: Int,
        curID: Int,
        prevRow: Int,
        prevCol: Int,
        row: Int,
        col: Int,
    ): Boolean {
        var deltaRow: Int
        var fromPrev: Int
        var fromCur: Int
        var toPrev: Int
        var toCur: Int
        var isMelded = false

        for (element1 in H_MELDS) {
            for (element2 in element1.value) {
                for (element3 in element2.value) {
                    for (element4 in element3.value) {
                        if ((row < rows - 1) &&
                            (element1.key == prevID) &&
                            (element2.key == curID) &&
                            (element3.key == toGrid[prevRow + 1][prevCol]) &&
                            (element4.key == toGrid[row + 1][col])
                        ) {
                            // From-top-to-bottom meld while traversing right.
                            deltaRow = 1
                            fromPrev = H_MELDS_DATA[element4.value][0]
                            fromCur = H_MELDS_DATA[element4.value][1]
                            toPrev = H_MELDS_DATA[element4.value][2]
                            toCur = H_MELDS_DATA[element4.value][3]
                        } else {
                            if ((row < rows - 1) &&
                                (element1.key == curID) &&
                                (element2.key == prevID) &&
                                (element3.key == toGrid[row + 1][col]) &&
                                (element4.key == toGrid[prevRow + 1][prevCol])
                            ) {
                                // From-top-to-bottom meld while traversing left.
                                deltaRow = 1
                                fromPrev = H_MELDS_DATA[element4.value][1]
                                fromCur = H_MELDS_DATA[element4.value][0]
                                toPrev = H_MELDS_DATA[element4.value][3]
                                toCur = H_MELDS_DATA[element4.value][2]
                            } else {

                                if ((row > 0) &&
                                    (element1.key == toGrid[prevRow - 1][prevCol]) &&
                                    (element2.key == toGrid[row - 1][col]) &&
                                    (element3.key == prevID) &&
                                    (element4.key == curID)
                                ) {
                                    // From-bottom-to-top meld while traversing right.
                                    deltaRow = -1
                                    fromPrev = H_MELDS_DATA[element4.value][2]
                                    fromCur = H_MELDS_DATA[element4.value][3]
                                    toPrev = H_MELDS_DATA[element4.value][0]
                                    toCur = H_MELDS_DATA[element4.value][1]
                                } else {
                                    if ((row > 0) &&
                                        (element1.key == toGrid[row - 1][col]) &&
                                        (element2.key == toGrid[prevRow - 1][prevCol]) &&
                                        (element3.key == curID) &&
                                        (element4.key == prevID)
                                    ) {
                                        // From-bottom-to-top meld while traversing left.
                                        deltaRow = -1
                                        fromPrev = H_MELDS_DATA[element4.value][3]
                                        fromCur = H_MELDS_DATA[element4.value][2]
                                        toPrev = H_MELDS_DATA[element4.value][1]
                                        toCur = H_MELDS_DATA[element4.value][0]
                                    } else continue
                                }
                            }
                        }

                        // Meld the knots
                        transferGrid[prevRow][prevCol] = fromPrev
                        transferGrid[row][col] = fromCur
                        transferGrid[prevRow + deltaRow][prevCol] = toPrev
                        transferGrid[row + deltaRow][col] = toCur

                        // Mark changes
                        changesGrid[prevRow][prevCol] = fromPrev
                        changesGrid[row][col] = fromCur
                        changesGrid[prevRow + deltaRow][prevCol] = toPrev
                        changesGrid[row + deltaRow][col] = toCur

                        // Delete old IDs
                        fromGrid[prevRow][prevCol] = BORDER_ID
                        fromGrid[row][col] = BORDER_ID

                        isMelded = true
                        break
                    }
                    if (isMelded) break
                }
                if (isMelded) break
            }
            if (isMelded) break
        }

        return isMelded
    }
}