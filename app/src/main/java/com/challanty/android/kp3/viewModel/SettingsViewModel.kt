package com.challanty.android.kp3.viewModel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.challanty.android.kp3.data.repository.Repository
import com.challanty.android.kp3.util.LIST_OF_ALL_DIMENSIONS
import com.challanty.android.kp3.util.LIST_OF_ALL_DIMENSIONS_EXCEPT_1
import com.challanty.android.kp3.util.LIST_OF_EVEN_DIMENSIONS
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for the Settings screen.
 */
@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val repository: Repository
) : ViewModel() {

    // User's in-progress changes (these persist across navigation)
    private val _pendingBoardRows = MutableStateFlow<Int?>(null)
    private val _pendingBoardCols = MutableStateFlow<Int?>(null)
    private val _pendingTileRows = MutableStateFlow<Int?>(null)
    private val _pendingTileCols = MutableStateFlow<Int?>(null)
    private val _pendingLockPercent = MutableStateFlow<Int?>(null)
    private val _pendingTilesRotatable = MutableStateFlow<Boolean?>(null)
    private val _pendingAnimate = MutableStateFlow<Boolean?>(null)
    private val _pendingAnimateRotation = MutableStateFlow<Boolean?>(null)
    private val _pendingAnimateSwap = MutableStateFlow<Boolean?>(null)
    private val _pendingSingleKnot = MutableStateFlow<Boolean?>(null)

    // Exposed values that combine current and pending values
    // Note: combine() only makes a Flow, so we need to convert back to a StateFlow
    val settingsBoardRows: StateFlow<Int> = combine(
        repository.settingsDataStoreStateFlow,
        _pendingBoardRows
    ) { settings, pending ->
        pending ?: settings.boardRows
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = 0
    )

    val settingsBoardCols: StateFlow<Int> = combine(
        repository.settingsDataStoreStateFlow,
        _pendingBoardCols
    ) { settings, pending ->
        pending ?: settings.boardCols
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = 0
    )

    val settingsTileRows: StateFlow<Int> = combine(
        repository.settingsDataStoreStateFlow,
        _pendingTileRows
    ) { settings, pending ->
        pending ?: settings.tileRows
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = 0
    )

    val settingsTileCols: StateFlow<Int> = combine(
        repository.settingsDataStoreStateFlow,
        _pendingTileCols
    ) { settings, pending ->
        pending ?: settings.tileCols
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = 0
    )

    val settingsLockPercent: StateFlow<Int> = combine(
        repository.settingsDataStoreStateFlow,
        _pendingLockPercent
    ) { settings, pending ->
        pending ?: settings.lockPercent
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = 0
    )

    val settingsTilesRotatable: StateFlow<Boolean> = combine(
        repository.settingsDataStoreStateFlow,
        _pendingTilesRotatable
    ) { settings, pending ->
        pending ?: settings.tilesRotatable
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = false
    )

    val settingsAnimate: StateFlow<Boolean> = combine(
        repository.settingsDataStoreStateFlow,
        _pendingAnimate
    ) { settings, pending ->
        pending ?: settings.animate
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = false
    )

    val settingsAnimateRotation: StateFlow<Boolean> = combine(
        repository.settingsDataStoreStateFlow,
        _pendingAnimateRotation
    ) { settings, pending ->
        pending ?: settings.animateRotation
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = false
    )

    val settingsAnimateSwap: StateFlow<Boolean> = combine(
        repository.settingsDataStoreStateFlow,
        _pendingAnimateSwap
    ) { settings, pending ->
        pending ?: settings.animateSwap
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = false
    )

    val settingsSingleKnot: StateFlow<Boolean> = combine(
        repository.settingsDataStoreStateFlow,
        _pendingSingleKnot
    ) { settings, pending ->
        pending ?: settings.singleKnot
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = false
    )

    // Whether tiles can be rotated based on tile dimensions
    val settingsCanRotateTiles: StateFlow<Boolean> = combine(
        settingsTileRows,
        settingsTileCols
    ) { rows, columns ->
        rows == columns // Tiles can only be rotated if they are square
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = false
    )

    val settingsValidBoardRowValues: StateFlow<List<Int>> = combine(
        settingsBoardRows,
        settingsBoardCols
    ) { boardRowsValue, boardColsValue ->
        if (boardColsValue == 1) {
            // Board rows and cols can't both be 1
            LIST_OF_ALL_DIMENSIONS_EXCEPT_1
        } else {
            LIST_OF_ALL_DIMENSIONS
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = LIST_OF_ALL_DIMENSIONS
    )

    val settingsValidBoardColValues: StateFlow<List<Int>> = combine(
        settingsBoardRows,
        settingsBoardCols
    ) { boardRowsValue, boardColsValue ->
        if (boardRowsValue == 1) {
            // Board rows and cols can't both be 1
            LIST_OF_ALL_DIMENSIONS_EXCEPT_1
        } else {
            LIST_OF_ALL_DIMENSIONS
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = LIST_OF_ALL_DIMENSIONS
    )

    val settingsValidTileRowValues: StateFlow<List<Int>> = combine(
        settingsBoardRows,
        settingsTileRows
    ) { boardRowsValue, tileRowsValue ->
        if (boardRowsValue % 2 == 1) {
            // If board rows is odd, only allow even tile rows
            LIST_OF_EVEN_DIMENSIONS
        } else {
            // If board rows is even, allow all tile row values
            LIST_OF_ALL_DIMENSIONS
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = LIST_OF_ALL_DIMENSIONS
    )

    val settingsValidTileColValues: StateFlow<List<Int>> = combine(
        settingsBoardCols,
        settingsTileCols
    ) { boardColsValue, tileColsValue ->
        if (boardColsValue % 2 == 1) {
            // If board columns is odd, only allow even tile columns
            LIST_OF_EVEN_DIMENSIONS
        } else {
            // If board columns is even, allow all values
            LIST_OF_ALL_DIMENSIONS
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = LIST_OF_ALL_DIMENSIONS
    )

    // Check if board dimensions have pending changes
    // Note: There are no subscribers to this flow, so it must be started Eagerly
    private val hasBoardPendingChanges: StateFlow<Boolean> = combine(
        repository.settingsDataStoreStateFlow,
        _pendingBoardRows,
        _pendingBoardCols
    ) { settings, pendingBoardR, pendingBoardC ->
        (pendingBoardR != null && pendingBoardR != settings.boardRows) ||
                (pendingBoardC != null && pendingBoardC != settings.boardCols)
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Eagerly,
        initialValue = false
    )

    // Check if tile dimensions have pending changes
    // Note: There are no subscribers to this flow, so it must be started Eagerly
    private val hasTilePendingChanges: StateFlow<Boolean> = combine(
        repository.settingsDataStoreStateFlow,
        _pendingTileRows,
        _pendingTileCols,
        _pendingTilesRotatable
    ) { settings, pendingTileR, pendingTileC, pendingRotatable ->
        (pendingTileR != null && pendingTileR != settings.tileRows) ||
                (pendingTileC != null && pendingTileC != settings.tileCols) ||
                (pendingRotatable != null && pendingRotatable != settings.tilesRotatable)
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Eagerly,
        initialValue = false
    )

    // Check if animation settings have pending changes
    // Note: There are no subscribers to this flow, so it must be started Eagerly
    private val hasAnimationPendingChanges: StateFlow<Boolean> = combine(
        repository.settingsDataStoreStateFlow,
        _pendingAnimate,
        _pendingAnimateRotation,
        _pendingAnimateSwap,
        _pendingSingleKnot
    ) { settings, pendingAnimate, pendingAnimateRotation, pendingAnimateSwap, pendingSingleKnot ->
        (pendingAnimate != null && pendingAnimate != settings.animate) ||
                (pendingAnimateRotation != null && pendingAnimateRotation != settings.animateRotation) ||
                (pendingAnimateSwap != null && pendingAnimateSwap != settings.animateSwap) ||
                (pendingSingleKnot != null && pendingSingleKnot != settings.singleKnot)
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Eagerly,
        initialValue = false
    )

    // Flag to indicate if there are any pending changes
    // Note: There are no subscribers to this flow, so it must be started Eagerly
    val hasPendingChanges: StateFlow<Boolean> = combine(
        hasBoardPendingChanges,
        hasTilePendingChanges,
        hasAnimationPendingChanges
    ) { hasMatrixChanges, hasTileChanges, hasAnimationChanges ->
        hasMatrixChanges || hasTileChanges || hasAnimationChanges
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.Eagerly,
        initialValue = false
    )

    fun setPendingBoardRows(value: Int) {
        _pendingBoardRows.value = value

        // If board rows is now odd, ensure tile rows is even
        if (value % 2 == 1) {
            val settingsTileRowsValue: Int = settingsTileRows.value

            if (settingsTileRowsValue % 2 == 1) {
                // Current tile rows is odd, need to adjust
                if (settingsTileRowsValue > 1) {
                    // Decrease by 1 to make it even
                    _pendingTileRows.value = settingsTileRowsValue - 1
                } else {
                    // If it's 1, set to 2
                    _pendingTileRows.value = 2
                }
            }
        }
    }

    fun setPendingBoardCols(value: Int) {
        _pendingBoardCols.value = value

        // If board columns is now odd, ensure tile columns is even
        if (value % 2 == 1) {
            val settingsTileColsValue = settingsTileCols.value

            if (settingsTileColsValue % 2 == 1) {
                // Current tile columns is odd, need to adjust
                if (settingsTileColsValue > 1) {
                    // Decrease by 1 to make it even
                    _pendingTileCols.value = settingsTileColsValue - 1
                } else {
                    // If it's 1, set to 2
                    _pendingTileCols.value = 2
                }
            }
        }
    }

    fun setPendingTileRows(value: Int) {
        // Made sure an odd tile rows didn't sneak through when board rows is odd
        if (settingsBoardRows.value % 2 == 1 && value % 2 == 1) {
            return
        }

        _pendingTileRows.value = value

        // Check if we need to update the rotatable setting
        updateRotatableSetting()
    }

    fun setPendingTileCols(value: Int) {
        // Made sure an odd tile cols didn't sneak through when board cols is odd
        if (settingsBoardCols.value % 2 == 1 && value % 2 == 1) {
            return
        }

        _pendingTileCols.value = value

        // Check if we need to update the rotatable setting
        updateRotatableSetting()
    }

    fun setPendingLockPercent(value: Int) {
        _pendingLockPercent.value = value
    }

    fun setPendingTilesRotatable(value: Boolean) {
        // Only allow setting to true if tiles are square
        if (value && settingsTileRows.value != settingsTileCols.value) {
            return
        }

        _pendingTilesRotatable.value = value
    }

    private fun updateRotatableSetting() {
        // If tiles are not square, force rotatable to false
        if (settingsTileRows.value != settingsTileCols.value) {
            _pendingTilesRotatable.value = false
        }
    }

    fun setPendingAnimate(value: Boolean) {
        _pendingAnimate.value = value
    }

    fun setPendingAnimateRotation(value: Boolean) {
        _pendingAnimateRotation.value = value
    }

    fun setPendingAnimateSwap(value: Boolean) {
        _pendingAnimateSwap.value = value
    }

    fun setPendingSingleKnot(value: Boolean) {
        _pendingSingleKnot.value = value
    }

    fun applyPendingChanges() {
        // If there are no pending changes, do nothing
        if (!hasPendingChanges.value) return

        val pendingBoardRows = _pendingBoardRows.value
        val pendingBoardCols = _pendingBoardCols.value
        val pendingTileRows = _pendingTileRows.value
        val pendingTileCols = _pendingTileCols.value
        val pendingLockPercent = _pendingLockPercent.value
        val pendingTilesRotatable = _pendingTilesRotatable.value
        val pendingAnimate = _pendingAnimate.value
        val pendingAnimateRotation = _pendingAnimateRotation.value
        val pendingAnimateSwap = _pendingAnimateSwap.value
        val pendingSingleKnot = _pendingSingleKnot.value

        val settings = repository.settingsDataStoreStateFlow.value

        // TODO is there an easier way to do this?
        // Note: The whole Settings datastore message gets written, so we
        // need to supply values for all fields.
        repository.pendingSetVersion(settings.version)
        repository.pendingSetBoardRows(pendingBoardRows ?: settings.boardRows)
        repository.pendingSetBoardCols(pendingBoardCols ?: settings.boardCols)
        repository.pendingSetTileRows(pendingTileRows ?: settings.tileRows)
        repository.pendingSetTileCols(pendingTileCols ?: settings.tileCols)
        repository.pendingSetLockPercent(pendingLockPercent ?: settings.lockPercent)
        repository.pendingSetTilesRotatable(pendingTilesRotatable ?: settings.tilesRotatable)
        repository.pendingSetAnimate(pendingAnimate ?: settings.animate)
        repository.pendingSetAnimateRotation(pendingAnimateRotation ?: settings.animateRotation)
        repository.pendingSetAnimateSwap(pendingAnimateSwap ?: settings.animateSwap)
        repository.pendingSetSingleKnot(pendingSingleKnot ?: settings.singleKnot)

        viewModelScope.launch {
            repository.onPendingFinalized()
        }

        // Clear pending changes after applying them
        _pendingBoardRows.value = null
        _pendingBoardCols.value = null
        _pendingTileRows.value = null
        _pendingTileCols.value = null
        _pendingLockPercent.value = null
        _pendingTilesRotatable.value = null
        _pendingAnimate.value = null
        _pendingAnimateRotation.value = null
        _pendingAnimateSwap.value = null
        _pendingSingleKnot.value = null
    }

    /**
     * Called when the ViewModel is being cleared (e.g., when the user navigates away
     * or when the app is shut down).
     */
    override fun onCleared() {
        super.onCleared()
        // Apply any pending changes before the ViewModel is cleared
        if (hasPendingChanges.value) {
            applyPendingChanges()
        }
    }
}
