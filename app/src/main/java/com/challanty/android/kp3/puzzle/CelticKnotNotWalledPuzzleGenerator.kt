package com.challanty.android.kp3.puzzle

import com.challanty.android.kp3.util.BORDER_ID
import com.challanty.android.kp3.util.BOT_N
import com.challanty.android.kp3.util.IS_BOT_N
import com.challanty.android.kp3.util.IS_RHT_N
import com.challanty.android.kp3.util.RHT_N
import kotlinx.coroutines.yield
import kotlin.random.Random

enum class KnotMakeCode(val value: Int) {
    TooManyRetries(0),
    Success(1),
    Failure(2),
    InternalError(3)
}

class CelticKnotNotWalledPuzzleGenerator {

    init {
        initNeighborsData()
    }

    suspend fun generate(puzzle: CelticKnotPuzzle): KnotMakeCode {
        val solution = puzzle.solution

        if (solution.isEmpty()) return KnotMakeCode.Success

        val rows = puzzle.rows
        val cols = puzzle.cols
        val seed = puzzle.seed

        // Create a random number generator, optionally with a seed
        val random = if (seed != null) Random(seed) else Random(System.currentTimeMillis())

        val hist = mutableListOf<Pair<Int, Int>>()
        val rowRetries = IntArray(rows)
        val rowRetryCnt = IntArray(rows)

        var row = 0
        var col = -1

        while (true) {
            col++
            if (col >= cols) {
                col = 0
                row++
                if (row >= rows) {
                    return KnotMakeCode.Success
                }
            }

            // Randomly select a character that fits the left neighbor.
            var remain = if (col == 0) RHT_N[BORDER_ID]!!.size else RHT_N[solution[row][col - 1]]!!.size
            var idx = random.nextInt(from = 0, until = remain)

            // Loop until a fit is found for the current space or a previous space
            while (true) {
                val choice =
                    if (col == 0) RHT_N[BORDER_ID]!![idx] else RHT_N[solution[row][col - 1]]!![idx]

                // We already know the chosen char fits the left neighbor.
                // Does this character fit the top neighbor (whether border or knot)
                // and any existing right or bottom borders.
                val tN = if (row <= 0) BORDER_ID else solution[row - 1][col]

                if ((IS_BOT_N[tN]!![choice] == true) &&
                    (col < cols - 1 || IS_RHT_N[choice]!![BORDER_ID] == true) &&
                    (row < rows - 1 || IS_BOT_N[choice]!![BORDER_ID] == true)
                ) {

                    // A match!  Put it into the grid.
                    // Remember where we are in the process in case we have to
                    // back up and make a change.
                    // Continue with the next grid space.
                    solution[row][col] = choice
                    hist.add(Pair(remain, idx))
                    break
                }

                // Loop until we have a new char to try for this or a previous cell
                while (true) {
                    idx++
                    remain--

                    // Wrap around if we run off the end of choices according to left neighbor
                    if (idx >= if (col == 0) RHT_N[BORDER_ID]!!.size else RHT_N[solution[row][col - 1]]!!.size) {
                        idx = 0
                    }

                    // Try new char (exit loop) if another choice is available
                    if (remain > 0) {
                        break
                    } else {
                        // Backup and loop again
                        solution[row][col] = 0 // Helps debugging, otherwise unnecessary

                        col--
                        if (col == -1) {
                            col = cols - 1
                            if (rowRetries[row]++ > 1000) {
                                yield()
                                rowRetries[row] = 0
                                rowRetryCnt[row]++
                                if (rowRetryCnt[row] > 10) {
                                    return KnotMakeCode.TooManyRetries
                                }
                            }
                            row--
                            if (row == -1) {
                                return KnotMakeCode.Failure
                            }
                        }

                        if (hist.isEmpty()) {
                            return KnotMakeCode.InternalError
                        } else {
                            val pair = hist.removeAt(hist.lastIndex)
                            remain = pair.first
                            idx = pair.second
                        }
                    }
                }
            }
        }
    }

    private fun initNeighborsData() {
        // Use the list of all valid right and bottom PUs to initialize isRn and isBn
        for (i in RHT_N.keys) {
            val rnList = RHT_N[i]!!
            val myMap = rnList.associateWith { true }
            IS_RHT_N[i] = myMap.toMutableMap()
        }

        for (i in BOT_N.keys) {
            val bnList = BOT_N[i]!!
            val myMap = bnList.associateWith { true }
            IS_BOT_N[i] = myMap.toMutableMap()
        }

        // Since BORDER_ID is not a valid knot PU it isn't in the rN and bN lists
        // it didn't get included in the above loops even though the BORDER_ID is a
        // valid neighbor for various PUs. We manually specify the valid neighbors
        // here.
        IS_RHT_N[0]!![BORDER_ID] = true
        IS_RHT_N[4]!![BORDER_ID] = true
        IS_RHT_N[10]!![BORDER_ID] = true
        IS_RHT_N[14]!![BORDER_ID] = true
        IS_RHT_N[21]!![BORDER_ID] = true
        IS_RHT_N[22]!![BORDER_ID] = true
        IS_RHT_N[24]!![BORDER_ID] = true

        IS_BOT_N[1]!![BORDER_ID] = true
        IS_BOT_N[5]!![BORDER_ID] = true
        IS_BOT_N[11]!![BORDER_ID] = true
        IS_BOT_N[15]!![BORDER_ID] = true
        IS_BOT_N[22]!![BORDER_ID] = true
        IS_BOT_N[23]!![BORDER_ID] = true
        IS_BOT_N[25]!![BORDER_ID] = true
    }
}