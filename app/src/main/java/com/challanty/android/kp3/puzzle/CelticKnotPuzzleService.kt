package com.challanty.android.kp3.puzzle

import com.challanty.android.kp3.pictureUnits.original.AbstractCelticPicUnit
import com.challanty.android.kp3.util.BORDER_ID
import com.challanty.android.kp3.util.FLIP_OU
import com.challanty.android.kp3.util.HEADING_DOWN
import com.challanty.android.kp3.util.HEADING_LEVEL
import com.challanty.android.kp3.util.HEADING_UP
import com.challanty.android.kp3.util.IN_2_OUT
import kotlin.random.Random

class CelticKnotPuzzleService {

    val celticKnotPuzzleUnifier = CelticKnotPuzzleUnifier()

    fun scramble(puzzle: CelticKnotPuzzle) {
        val solution = puzzle.solution

        if (solution.isEmpty()) return

        val scrambled = puzzle.scrambled
        val puzzleRows = puzzle.rows
        val puzzleCols = puzzle.cols
        val boardRows = puzzle.boardRows
        val boardCols = puzzle.boardCols
        val tileRows = puzzle.tileRows
        val tileCols = puzzle.tileCols
        val doRotations = puzzle.doRotations
        val seed = puzzle.seed

        // Can't scramble if the puzzle dimensions
        // are not divisible by the tile dimensions
        if (puzzleRows % tileRows != 0 || puzzleCols % tileCols != 0) {
            return
        }

        // Create a random number generator, optionally with a seed
        val random = if (seed != null) Random(seed) else Random(System.currentTimeMillis())

        // Create a list of all tile positions and...
        val shuffledPositions = mutableListOf<Pair<Int, Int>>()
        for (boardRow in 0 until boardRows) {
            for (boardCol in 0 until boardCols) {
                shuffledPositions.add(Pair(boardRow, boardCol))
            }
        }
        // ...shuffle them
        shuffledPositions.shuffle(random)

        val squareSize = tileRows  // Square size is the same as tile rows or columns

        for (boardRow in 0 until boardRows) {
            for (boardCol in 0 until boardCols) {
                val newGamePos = shuffledPositions[(boardRow * boardCols) + boardCol]
                // Calculate the rotation (0, 90, 180, or 270 degrees)
                // Only rotate if tiles are square
                val quarterTurns = if (doRotations && tileRows == tileCols) {
                    random.nextInt(4)
                } else {
                    0
                }

                // Copy and rotate the tile
                for (tileRow in 0 until squareSize) {
                    for (tileCol in 0 until squareSize) {
                        val srcRow = boardRow * squareSize + tileRow
                        val srcCol = boardCol * squareSize + tileCol

                        // Calculate the rotated position of the picture unit
                        val (rotatedTileRow, rotatedTileCol) = when (quarterTurns) {
                            0 -> Pair(tileRow, tileCol)
                            1 -> Pair(tileCol, squareSize - 1 - tileRow)
                            2 -> Pair(squareSize - 1 - tileRow, squareSize - 1 - tileCol)
                            3 -> Pair(squareSize - 1 - tileCol, tileRow)
                            else -> Pair(tileRow, tileCol) // Default to no rotation
                        }

                        // Calculate the destination position in the scrambled matrix
                        val destRow = newGamePos.first * tileRows + rotatedTileRow
                        val destCol = newGamePos.second * tileCols + rotatedTileCol

                        // Rotate the picture unit and copy it to the destination position
                        scrambled[destRow][destCol] = AbstractCelticPicUnit.Companion.rotatePicUnit(
                            picUnit = solution[srcRow][srcCol],
                            quarterTurns = quarterTurns
                        )
                    }
                }
            }
        }
    }

    fun swapTiles(
        puzzle: CelticKnotPuzzle,
        boardRow1: Int,
        boardCol1: Int,
        boardRow2: Int,
        boardCol2: Int,
    ) {
        val picUnitMatrix = puzzle.scrambled
        val tileRows = puzzle.tileRows
        val tileCols = puzzle.tileCols

        val startRow1 = boardRow1 * tileRows
        val startCol1 = boardCol1 * tileCols
        val startRow2 = boardRow2 * tileRows
        val startCol2 = boardCol2 * tileCols

        // Swap the picture units
        for (row in 0 until tileRows) {
            for (col in 0 until tileCols) {
                val row1 = startRow1 + row
                val col1 = startCol1 + col
                val row2 = startRow2 + row
                val col2 = startCol2 + col
                val temp = picUnitMatrix[row1][col1]
                picUnitMatrix[row1][col1] = picUnitMatrix[row2][col2]
                picUnitMatrix[row2][col2] = temp
            }
        }
    }

    fun rotateTile(
        puzzle: CelticKnotPuzzle,
        boardRow: Int,
        boardCol: Int,
    ) {
        val picUnitMatrix = puzzle.scrambled
        val squareTileSize = puzzle.tileRows // Square size is the same as tile rows or columns

        val startRow = boardRow * squareTileSize
        val startCol = boardCol * squareTileSize

        // Determine the rotated tile
        val rotatedTile = Array(squareTileSize) { IntArray(squareTileSize) }
        for (row in 0 until squareTileSize) {
            for (col in 0 until squareTileSize) {
                rotatedTile[col][squareTileSize - 1 - row] =
                    AbstractCelticPicUnit.Companion.rotatePicUnit(
                        picUnitMatrix[startRow + row][startCol + col],
                        1
                    )
            }
        }

        // Replace the original tile with the rotated tile
        for (row in 0 until squareTileSize) {
            for (col in 0 until squareTileSize) {
                picUnitMatrix[startRow + row][startCol + col] = rotatedTile[row][col]
            }
        }
    }

    fun fixOU(
        puzzle: CelticKnotPuzzle
    ) {
        // Fix the over/under state of all knots

        val fromGrid = puzzle.solution
        val toGrid = puzzle.scrambled
        val rows = puzzle.rows
        val cols = puzzle.cols

        while (true) {
            var found = false
            for (row in 0 until rows) {
                for (col in 0 until cols) {
                    if (fromGrid[row][col] != BORDER_ID) {
                        fixKnotOU(
                            fromGrid = fromGrid,
                            toGrid = toGrid,
                            startRow = row,
                            startCol = col
                        )
                        found = true
                        break
                    }
                }
                // If we found a knot, start over and look for more. Otherwise, we're done.
                if (found) break
            }
            // If we didn't find any knots, we're done. Otherwise, keep looking.
            if (!found) break
        }

        // Copy fixed knot(s) back into the solution
        for (row in 0 until rows) {
            for (col in 0 until cols) {
                fromGrid[row][col] = toGrid[row][col]
            }
        }
    }

    fun fixKnotOU(
        fromGrid: Array<IntArray>,
        toGrid: Array<IntArray>,
        startRow: Int,
        startCol: Int,
    ) {
        // Transfer the knot from fromGrid to toGrid with any over/under inconsistencies fixed.

        var row = startRow
        var col = startCol
        var prevRow: Int
        var prevCol: Int
        var prevEnt: Int
        var curID: Int
        var transferID: Int
        var curHeading: Int
        var prevHeading: Int = HEADING_LEVEL // Placeholder value

        // Get one of the entrance points for the current ID
        var ent = entrance(fromGrid[row][col])

        // Determine the starting OVER/UNDER state of the knot.
        // This will be the starting prevState when we next traverse the knot.
        var found = false
        do {
            curHeading = IN_2_OUT[fromGrid[row][col]]!![ent]!!.heading
            if (curHeading != HEADING_LEVEL) {
                prevHeading = if (curHeading == HEADING_UP) {
                    HEADING_DOWN
                } else {
                    HEADING_UP
                }

                found = true

            } else {
                prevEnt = ent
                prevRow = row
                prevCol = col

                ent = IN_2_OUT[fromGrid[prevRow][prevCol]]!![prevEnt]!!.nextEnt
                row += IN_2_OUT[fromGrid[prevRow][prevCol]]!![prevEnt]!!.delta.first
                col += IN_2_OUT[fromGrid[prevRow][prevCol]]!![prevEnt]!!.delta.second
            }

        } while ((!found) && ((row != startRow) || (col != startCol)))

        if (!found) {
            // Knot has no over/under state change, just need to transfer the knot
            prevHeading = HEADING_LEVEL
        }

        // Make sure that when traversing from one id to another that
        // they are both heading over, under, or level.
        row = startRow
        col = startCol
        ent = entrance(fromGrid[row][col])

        do {
            // Figure next position.
            prevEnt = ent
            prevRow = row
            prevCol = col

            // We assume no O/U change is needed. Thus, the transfer ID is simply the current ID.
            curID = fromGrid[prevRow][prevCol]
            transferID = curID

            try {
                ent = IN_2_OUT[curID]!![prevEnt]!!.nextEnt
                row += IN_2_OUT[curID]!![prevEnt]!!.delta.first
                col += IN_2_OUT[curID]!![prevEnt]!!.delta.second
            } catch (e: Exception) {
                // Invalid traversal
                // TODO report this to analytics
                return
            }

            // Over/under violations can only happen at diagonals (odd entry numbers)
            if ((prevEnt % 2) == 1) {
                curHeading = IN_2_OUT[curID]!![prevEnt]!!.heading

                // Bad over/under conjunction?
                if (prevHeading == curHeading) {
                    // Over/under violation.  Corrective surgery needed. Flip OU of id.
                    transferID = FLIP_OU[curID]!!
                }

                // Our new heading is reverse of the old
                prevHeading = if (prevHeading == HEADING_UP) {
                    HEADING_DOWN
                } else {
                    HEADING_UP
                }

            } else if ((ent % 2) == 1) {
                // We are going to enter the next space on a diagonal (odd number),
                // We are therefore at the beginning of an over or under and our
                // previous and current headings need to be the same.
                curHeading = IN_2_OUT[curID]!![prevEnt]!!.heading

                // Bad transition?
                if (prevHeading != curHeading) {
                    // Over/under violation. Corrective surgery needed.
                    transferID = FLIP_OU[curID]!!
                }
            }

            toGrid[prevRow][prevCol] = transferID
            fromGrid[prevRow][prevCol] = BORDER_ID

        } while ((row != startRow) || (col != startCol))
    }

    fun unifyKnots(
        puzzle: CelticKnotPuzzle,
    ) {
        celticKnotPuzzleUnifier.unifyKnots(puzzle)
    }

    private fun entrance(id: Int): Int {
        return IN_2_OUT[id]!!.keys.first()
    }
}