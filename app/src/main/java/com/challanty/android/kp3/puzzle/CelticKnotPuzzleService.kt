package com.challanty.android.kp3.puzzle

import com.challanty.android.kp3.pictureUnits.original.AbstractCelticPicUnit
import com.challanty.android.kp3.util.BORDER_ID
import com.challanty.android.kp3.util.FLIP_OU
import com.challanty.android.kp3.util.HEADING_DOWN
import com.challanty.android.kp3.util.HEADING_LEVEL
import com.challanty.android.kp3.util.HEADING_UP
import com.challanty.android.kp3.util.H_MELDS
import com.challanty.android.kp3.util.H_MELDS_DATA
import com.challanty.android.kp3.util.IN_2_OUT
import com.challanty.android.kp3.util.V_MELDS
import com.challanty.android.kp3.util.V_MELDS_DATA
import kotlinx.coroutines.yield
import kotlin.random.Random

class CelticKnotPuzzleService {

    fun scramble(puzzle: CelticKnotPuzzle) {
        val solution = puzzle.solution

        if (solution.isEmpty()) return

        val scrambled = puzzle.scrambled
        val puzzleRows = puzzle.rows
        val puzzleCols = puzzle.cols
        val boardRows = puzzle.boardRows
        val boardCols = puzzle.boardCols
        val tileRows = puzzle.tileRows
        val tileCols = puzzle.tileCols
        val doRotations = puzzle.doRotations
        val seed = puzzle.seed

        // Can't scramble if the puzzle dimensions
        // are not divisible by the tile dimensions
        if (puzzleRows % tileRows != 0 || puzzleCols % tileCols != 0) {
            return
        }

        // Create a random number generator, optionally with a seed
        val random = if (seed != null) Random(seed) else Random(System.currentTimeMillis())

        // Create a list of all tile positions and...
        val shuffledPositions = mutableListOf<Pair<Int, Int>>()
        for (boardRow in 0 until boardRows) {
            for (boardCol in 0 until boardCols) {
                shuffledPositions.add(Pair(boardRow, boardCol))
            }
        }
        // ...shuffle them
        shuffledPositions.shuffle(random)

        val squareSize = tileRows  // Square size is the same as tile rows or columns

        for (boardRow in 0 until boardRows) {
            for (boardCol in 0 until boardCols) {
                val newGamePos = shuffledPositions[(boardRow * boardCols) + boardCol]
                // Calculate the rotation (0, 90, 180, or 270 degrees)
                // Only rotate if tiles are square
                val quarterTurns = if (doRotations && tileRows == tileCols) {
                    random.nextInt(4)
                } else {
                    0
                }

                // Copy and rotate the tile
                for (tileRow in 0 until squareSize) {
                    for (tileCol in 0 until squareSize) {
                        val srcRow = boardRow * squareSize + tileRow
                        val srcCol = boardCol * squareSize + tileCol

                        // Calculate the rotated position of the picture unit
                        val (rotatedTileRow, rotatedTileCol) = when (quarterTurns) {
                            0 -> Pair(tileRow, tileCol)
                            1 -> Pair(tileCol, squareSize - 1 - tileRow)
                            2 -> Pair(squareSize - 1 - tileRow, squareSize - 1 - tileCol)
                            3 -> Pair(squareSize - 1 - tileCol, tileRow)
                            else -> Pair(tileRow, tileCol) // Default to no rotation
                        }

                        // Calculate the destination position in the scrambled matrix
                        val destRow = newGamePos.first * tileRows + rotatedTileRow
                        val destCol = newGamePos.second * tileCols + rotatedTileCol

                        // Rotate the picture unit and copy it to the destination position
                        scrambled[destRow][destCol] = AbstractCelticPicUnit.Companion.rotatePicUnit(
                            picUnit = solution[srcRow][srcCol],
                            quarterTurns = quarterTurns
                        )
                    }
                }
            }
        }
    }

    fun swapTiles(
        puzzle: CelticKnotPuzzle,
        boardRow1: Int,
        boardCol1: Int,
        boardRow2: Int,
        boardCol2: Int,
    ) {
        val picUnitMatrix = puzzle.scrambled
        val tileRows = puzzle.tileRows
        val tileCols = puzzle.tileCols

        val startRow1 = boardRow1 * tileRows
        val startCol1 = boardCol1 * tileCols
        val startRow2 = boardRow2 * tileRows
        val startCol2 = boardCol2 * tileCols

        // Swap the picture units
        for (row in 0 until tileRows) {
            for (col in 0 until tileCols) {
                val row1 = startRow1 + row
                val col1 = startCol1 + col
                val row2 = startRow2 + row
                val col2 = startCol2 + col
                val temp = picUnitMatrix[row1][col1]
                picUnitMatrix[row1][col1] = picUnitMatrix[row2][col2]
                picUnitMatrix[row2][col2] = temp
            }
        }
    }

    fun rotateTile(
        puzzle: CelticKnotPuzzle,
        boardRow: Int,
        boardCol: Int,
    ) {
        val picUnitMatrix = puzzle.scrambled
        val squareTileSize = puzzle.tileRows // Square size is the same as tile rows or columns

        val startRow = boardRow * squareTileSize
        val startCol = boardCol * squareTileSize

        // Determine the rotated tile
        val rotatedTile = Array(squareTileSize) { IntArray(squareTileSize) }
        for (row in 0 until squareTileSize) {
            for (col in 0 until squareTileSize) {
                rotatedTile[col][squareTileSize - 1 - row] =
                    AbstractCelticPicUnit.Companion.rotatePicUnit(
                        picUnitMatrix[startRow + row][startCol + col],
                        1
                    )
            }
        }

        // Replace the original tile with the rotated tile
        for (row in 0 until squareTileSize) {
            for (col in 0 until squareTileSize) {
                picUnitMatrix[startRow + row][startCol + col] = rotatedTile[row][col]
            }
        }
    }

    suspend fun fixOU(
        puzzle: CelticKnotPuzzle
    ) {
        // Fix the over/under state of all knots

        val fromGrid = puzzle.solution
        val toGrid = puzzle.scrambled
        val rows = puzzle.rows
        val cols = puzzle.cols

        while (true) {
            var found = false
            for (row in 0 until rows) {
                yield()
                for (col in 0 until cols) {
                    if (fromGrid[row][col] != BORDER_ID) {
                        fixKnotOU(
                            fromGrid = fromGrid,
                            toGrid = toGrid,
                            startRow = row,
                            startCol = col
                        )
                        found = true
                        break
                    }
                }
                // If we found a knot, start over and look for more. Otherwise, we're done.
                if (found) break
            }
            // If we didn't find any knots, we're done. Otherwise, keep looking.
            if (!found) break
        }

        // Copy fixed knot(s) back into the solution
        for (row in 0 until rows) {
            for (col in 0 until cols) {
                fromGrid[row][col] = toGrid[row][col]
            }
        }
    }

    fun fixKnotOU(
        fromGrid: Array<IntArray>,
        toGrid: Array<IntArray>,
        startRow: Int,
        startCol: Int,
    ) {
        // Transfer the knot from fromGrid to toGrid with any over/under inconsistencies fixed.

        var row = startRow
        var col = startCol
        var prevRow: Int
        var prevCol: Int
        var prevEnt: Int
        var curID: Int
        var transferID: Int
        var curHeading: Int
        var prevHeading: Int = HEADING_LEVEL // Placeholder value

        // Get one of the entrance points for the current ID
        var ent = entrance(fromGrid[row][col])

        // Determine the starting OVER/UNDER state of the knot.
        // This will be the starting prevState when we next traverse the knot.
        var found = false
        do {
            curHeading = IN_2_OUT[fromGrid[row][col]]!![ent]!!.heading
            if (curHeading != HEADING_LEVEL) {
                prevHeading = if (curHeading == HEADING_UP) {
                    HEADING_DOWN
                } else {
                    HEADING_UP
                }

                found = true

            } else {
                prevEnt = ent
                prevRow = row
                prevCol = col

                ent = IN_2_OUT[fromGrid[prevRow][prevCol]]!![prevEnt]!!.nextEnt
                row += IN_2_OUT[fromGrid[prevRow][prevCol]]!![prevEnt]!!.delta.first
                col += IN_2_OUT[fromGrid[prevRow][prevCol]]!![prevEnt]!!.delta.second
            }

        } while ((!found) && ((row != startRow) || (col != startCol)))

        if (!found) {
            // Knot has no over/under state change, just need to transfer the knot
            prevHeading = HEADING_LEVEL
        }

        // Make sure that when traversing from one id to another that
        // they are both heading over, under, or level.
        row = startRow
        col = startCol
        ent = entrance(fromGrid[row][col])

        do {
            // Figure next position.
            prevEnt = ent
            prevRow = row
            prevCol = col

            // We assume no O/U change is needed. Thus, the transfer ID is simply the current ID.
            curID = fromGrid[prevRow][prevCol]
            transferID = curID

            try {
                ent = IN_2_OUT[curID]!![prevEnt]!!.nextEnt
                row += IN_2_OUT[curID]!![prevEnt]!!.delta.first
                col += IN_2_OUT[curID]!![prevEnt]!!.delta.second
            } catch (e: Exception) {
                // Invalid traversal
                // TODO report this to analytics
                return
            }

            // Over/under violations can only happen at diagonals (odd entry numbers)
            if ((prevEnt % 2) == 1) {
                curHeading = IN_2_OUT[curID]!![prevEnt]!!.heading

                // Bad over/under conjunction?
                if (prevHeading == curHeading) {
                    // Over/under violation.  Corrective surgery needed. Flip OU of id.
                    transferID = FLIP_OU[curID]!!
                }

                // Our new heading is reverse of the old
                prevHeading = if (prevHeading == HEADING_UP) {
                    HEADING_DOWN
                } else {
                    HEADING_UP
                }

            } else if ((ent % 2) == 1) {
                // We are going to enter the next space on a diagonal (odd number),
                // We are therefore at the beginning of an over or under and our
                // previous and current headings need to be the same.
                curHeading = IN_2_OUT[curID]!![prevEnt]!!.heading

                // Bad transition?
                if (prevHeading != curHeading) {
                    // Over/under violation. Corrective surgery needed.
                    transferID = FLIP_OU[curID]!!
                }
            }

            toGrid[prevRow][prevCol] = transferID
            fromGrid[prevRow][prevCol] = BORDER_ID

        } while ((row != startRow) || (col != startCol))
    }

    suspend fun unifyKnots(
        puzzle: CelticKnotPuzzle
    ) {
        val fromGrid = puzzle.solution
        val toGrid = puzzle.scrambled
        val rows = puzzle.rows
        val cols = puzzle.cols

        var knotCount = 0
        var meldCount = 0

        while (true) {
            var found = false

            for (row in 0 until rows) {
                yield()
                for (col in 0 until cols) {
                    if (fromGrid[row][col] != BORDER_ID) {
                        if (meldKnots(
                                rows = rows,
                                cols = cols,
                                fromGrid = fromGrid,
                                toGrid = toGrid,
                                startRow = row,
                                startCol = col,
                                knotCount = knotCount,
                            )
                        ) ++meldCount
                        found = true
                        break
                    }
                }
                if (found) {
                    break
                }
            }

            if (!found) {
                break
            }
            ++knotCount
        }

        // Copy melded knot(s) into final knot(s)
        for (row in 0 until rows) {
            for (col in 0 until cols) {
                fromGrid[row][col] = toGrid[row][col]
            }
        }
    }

    private fun meldKnots(
        rows: Int,
        cols: Int,
        fromGrid: Array<IntArray>,
        toGrid: Array<IntArray>,
        startRow: Int,
        startCol: Int,
        knotCount: Int,
    ): Boolean {
        val transferGrid = Array(rows) { IntArray(cols) { BORDER_ID } }
        val changesGrid = Array(rows) { IntArray(cols) { BORDER_ID } }

        var row = startRow
        var col = startCol
        var prevRow = 0   // Placeholder value
        var prevCol = 0   // Placeholder value
        var prevID = 0    // Placeholder value
        var prevEnt: Int

        // Traverse the knot.  Stop when we get to the next char after the current char.
        var curID = fromGrid[row][col]
        var ent = entrance(curID)

        val stopRow = row + IN_2_OUT[curID]!![ent]!!.delta.first
        val stopCol = col + IN_2_OUT[curID]!![ent]!!.delta.second

        // Save first ID since it needs to be used in the
        // last knot ID pair but will have been removed
        // when it gets used in the first knot ID pair.
        val firstID = fromGrid[row][col]

        var isTraversalStart = true

        // The first knot is always considered melded by default since there's nothing yet to meld to.
        var isMelded = knotCount == 0

        while (true) {
            // Stop if we've looped all the way around a knot.
            // Note: We must make sure we aren't simply encountering the
            // stopping position as we start the knot traversal.
            if ((row == stopRow) && (col == stopCol)) {
                if (isTraversalStart) {
                    isTraversalStart = false
                } else {
                    break
                }
            }

            curID = if ((row == startRow) && (col == startCol)) {
                firstID
            } else {
                fromGrid[row][col]
            }

            // Once melded, the rest of the knot is simply put into transferGrid.
            if (isMelded) {
                transferGrid[row][col] = curID
                fromGrid[row][col] = BORDER_ID
            } else if (!isTraversalStart) {
                val rowDelta = row - prevRow
                val colDelta = col - prevCol

                isMelded = if (rowDelta == 0) {
                    tryHorizontalMeld(
                        rows = rows,
                        fromGrid = fromGrid,
                        toGrid = toGrid,
                        transferGrid = transferGrid,
                        changesGrid = changesGrid,
                        prevID = prevID,
                        curID = curID,
                        prevRow = prevRow,
                        prevCol = prevCol,
                        row = row,
                        col = col,
                    )
                } else if (colDelta == 0) {
                    tryVerticalMeld(
                        cols = cols,
                        fromGrid = fromGrid,
                        toGrid = toGrid,
                        transferGrid = transferGrid,
                        changesGrid = changesGrid,
                        prevID = prevID,
                        curID = curID,
                        prevRow = prevRow,
                        prevCol = prevCol,
                        row = row,
                        col = col,
                    )
                } else false
            }

            // Non-melded IDs go to the transferGrid.
            if (!isMelded) {
                transferGrid[row][col] = curID
                fromGrid[row][col] = BORDER_ID
            }

            // Go to next square (we may be going forward, backward, up, or down)
            prevEnt = ent
            prevRow = row
            prevCol = col
            prevID = curID

            ent = IN_2_OUT[prevID]!![prevEnt]!!.nextEnt
            row += IN_2_OUT[prevID]!![prevEnt]!!.delta.first
            col += IN_2_OUT[prevID]!![prevEnt]!!.delta.second
        }

        // Transfer knot to toGrid
        for (aRow in 0 until rows) {
            for (aCol in 0 until cols) {
                if (transferGrid[aRow][aCol] != BORDER_ID) {
                    toGrid[aRow][aCol] = transferGrid[aRow][aCol]

                    // Restore any clobbered change
                    if (changesGrid[aRow][aCol] != BORDER_ID) {
                        toGrid[aRow][aCol] = changesGrid[aRow][aCol]
                    }
                }
            }
        }

        return isMelded
    }

    private fun tryVerticalMeld(
        cols: Int,
        fromGrid: Array<IntArray>,
        toGrid: Array<IntArray>,
        transferGrid: Array<IntArray>,
        changesGrid: Array<IntArray>,
        prevID: Int,
        curID: Int,
        prevRow: Int,
        prevCol: Int,
        row: Int,
        col: Int,
    ): Boolean {
        var deltaCol: Int
        var fromPrev: Int
        var fromCur: Int
        var toPrev: Int
        var toCur: Int
        var isMelded = false

        for (element1 in V_MELDS) {
            for (element2 in element1.value) {
                for (element3 in element2.value) {
                    for (element4 in element3.value) {
                        if ((col > 0) &&
                            (element1.key == prevID) &&
                            (element2.key == curID) &&
                            (element3.key == toGrid[prevRow][prevCol - 1]) &&
                            (element4.key == toGrid[row][col - 1])
                        ) {
                            // R2L meld while traversing down.
                            // NOTE: This check must be first or this case
                            // won't work: y|y
                            //             y|y
                            deltaCol = -1
                            fromPrev = V_MELDS_DATA[element4.value][0]
                            fromCur = V_MELDS_DATA[element4.value][1]
                            toPrev = V_MELDS_DATA[element4.value][2]
                            toCur = V_MELDS_DATA[element4.value][3]
//                            printMeld(row, col, prevRow, prevCol, "R2L DOWN",
//                                toGrid[prevRow][prevCol - 1], prevID,
//                                toGrid[row][col - 1], curID,
//                                toPrev, fromPrev, toCur, fromCur)
                        } else if ((col > 0) &&
                            (element1.key == curID) &&
                            (element2.key == prevID) &&
                            (element3.key == toGrid[row][col - 1]) &&
                            (element4.key == toGrid[prevRow][prevCol - 1])
                        ) {
                            // R2L meld while traversing up.
                            deltaCol = -1
                            fromPrev = V_MELDS_DATA[element4.value][1]
                            fromCur = V_MELDS_DATA[element4.value][0]
                            toPrev = V_MELDS_DATA[element4.value][3]
                            toCur = V_MELDS_DATA[element4.value][2]
//                            printMeld(row, col, prevRow, prevCol,"R2L UP",
//                                toGrid[row][col - 1], curID,
//                                toGrid[prevRow][prevCol - 1], prevID,
//                                toCur, fromCur, toPrev, fromPrev)
                        } else if ((col < cols - 1) &&
                            (element1.key == toGrid[prevRow][prevCol + 1]) &&
                            (element2.key == toGrid[row][col + 1]) &&
                            (element3.key == prevID) &&
                            (element4.key == curID)
                        ) {
                            // L2R meld while traversing down.
                            deltaCol = 1
                            fromPrev = V_MELDS_DATA[element4.value][2]
                            fromCur = V_MELDS_DATA[element4.value][3]
                            toPrev = V_MELDS_DATA[element4.value][0]
                            toCur = V_MELDS_DATA[element4.value][1]
//                            printMeld(row, col, prevRow, prevCol,"L2R DOWN",
//                                toGrid[prevRow][prevCol + 1], prevID,
//                                toGrid[row][col + 1], curID,
//                                fromPrev, toPrev, fromCur, toCur)
                        } else if ((col < cols - 1) &&
                            (element1.key == toGrid[row][col + 1]) &&
                            (element2.key == toGrid[prevRow][prevCol + 1]) &&
                            (element3.key == curID) &&
                            (element4.key == prevID)
                        ) {
                            // From-left-to-right meld while traversing up.
                            deltaCol = 1
                            fromPrev = V_MELDS_DATA[element4.value][3]
                            fromCur = V_MELDS_DATA[element4.value][2]
                            toPrev = V_MELDS_DATA[element4.value][1]
                            toCur = V_MELDS_DATA[element4.value][0]
//                            printMeld(row, col, prevRow, prevCol,"L2R UP",
//                                curID, toGrid[row][col + 1],
//                                prevID, toGrid[prevRow][prevCol + 1],
//                                fromCur, toCur, fromPrev, toPrev)
                        } else continue

                        // Meld the knots
                        transferGrid[prevRow][prevCol] = fromPrev
                        transferGrid[row][col] = fromCur
                        transferGrid[prevRow][prevCol + deltaCol] = toPrev
                        transferGrid[row][col + deltaCol] = toCur

                        // Mark changes
                        changesGrid[prevRow][prevCol] = fromPrev
                        changesGrid[row][col] = fromCur
                        changesGrid[prevRow][prevCol + deltaCol] = toPrev
                        changesGrid[row][col + deltaCol] = toCur

                        // Delete old IDs
                        fromGrid[prevRow][prevCol] = BORDER_ID
                        fromGrid[row][col] = BORDER_ID

                        isMelded = true
                        break
                    }
                    if (isMelded) break
                }
                if (isMelded) break
            }
            if (isMelded) break
        }

        return isMelded
    }

    private fun tryHorizontalMeld(
        rows: Int,
        fromGrid: Array<IntArray>,
        toGrid: Array<IntArray>,
        transferGrid: Array<IntArray>,
        changesGrid: Array<IntArray>,
        prevID: Int,
        curID: Int,
        prevRow: Int,
        prevCol: Int,
        row: Int,
        col: Int,
    ): Boolean {
        var deltaRow: Int
        var fromPrev: Int
        var fromCur: Int
        var toPrev: Int
        var toCur: Int
        var isMelded = false

        for (element1 in H_MELDS) {
            for (element2 in element1.value) {
                for (element3 in element2.value) {
                    for (element4 in element3.value) {
//                        if (row < rows - 1) {
//                            printMeldTest(
//                                row = row,
//                                col = col,
//                                prevRow = prevRow,
//                                prevCol = prevCol,
//                                msg = "T2B RIGHT",
//                                a = element1.key,
//                                b = prevID,
//                                c = element2.key,
//                                d = curID,
//                                e = element3.key,
//                                f = toGrid[prevRow + 1][prevCol],
//                                g = element4.key,
//                                h = toGrid[row + 1][col],
//                            )
//                        }
                        if ((row < rows - 1) &&
                            (element1.key == prevID) &&
                            (element2.key == curID) &&
                            (element3.key == toGrid[prevRow + 1][prevCol]) &&
                            (element4.key == toGrid[row + 1][col])
                        ) {
                            // From-top-to-bottom meld while traversing right.
                            deltaRow = 1
                            fromPrev = H_MELDS_DATA[element4.value][0]
                            fromCur = H_MELDS_DATA[element4.value][1]
                            toPrev = H_MELDS_DATA[element4.value][2]
                            toCur = H_MELDS_DATA[element4.value][3]
//                            printMeld(row, col, prevRow, prevCol, "T2B RIGHT",
//                                prevID, curID,
//                                toGrid[prevRow + 1][prevCol], toGrid[row + 1][col],
//                                fromPrev, fromCur, toPrev, toCur)
                        } else {
//                            if (row < rows - 1) {
//                                printMeldTest(
//                                    row = row,
//                                    col = col,
//                                    prevRow = prevRow,
//                                    prevCol = prevCol,
//                                    msg = "T2B LEFT",
//                                    a = element1.key,
//                                    b = curID,
//                                    c = element2.key,
//                                    d = prevID,
//                                    e = element3.key,
//                                    f = toGrid[row + 1][col],
//                                    g = element4.key,
//                                    h = toGrid[prevRow + 1][prevCol],
//                                )
//                            }
                            if ((row < rows - 1) &&
                                (element1.key == curID) &&
                                (element2.key == prevID) &&
                                (element3.key == toGrid[row + 1][col]) &&
                                (element4.key == toGrid[prevRow + 1][prevCol])
                            ) {
                                // From-top-to-bottom meld while traversing left.
                                deltaRow = 1
                                fromPrev = H_MELDS_DATA[element4.value][1]
                                fromCur = H_MELDS_DATA[element4.value][0]
                                toPrev = H_MELDS_DATA[element4.value][3]
                                toCur = H_MELDS_DATA[element4.value][2]
//                            printMeld(row, col, prevRow, prevCol,"T2B LEFT",
//                                curID, prevID,
//                                toGrid[row + 1][col], toGrid[prevRow + 1][prevCol],
//                                fromCur, fromPrev, toCur, toPrev)
                            } else {
//                                if (row > 0) {
//                                    printMeldTest(
//                                        row = row,
//                                        col = col,
//                                        prevRow = prevRow,
//                                        prevCol = prevCol,
//                                        msg = "B2T RIGHT",
//                                        a = element1.key,
//                                        b = toGrid[prevRow - 1][prevCol],
//                                        c = element2.key,
//                                        d = toGrid[row - 1][col],
//                                        e = element3.key,
//                                        f = prevID,
//                                        g = element4.key,
//                                        h = curID,
//                                    )
//                                }
                                if ((row > 0) &&
                                    (element1.key == toGrid[prevRow - 1][prevCol]) &&
                                    (element2.key == toGrid[row - 1][col]) &&
                                    (element3.key == prevID) &&
                                    (element4.key == curID)
                                ) {
                                    // From-bottom-to-top meld while traversing right.
                                    deltaRow = -1
                                    fromPrev = H_MELDS_DATA[element4.value][2]
                                    fromCur = H_MELDS_DATA[element4.value][3]
                                    toPrev = H_MELDS_DATA[element4.value][0]
                                    toCur = H_MELDS_DATA[element4.value][1]
//                            printMeld(row, col, prevRow, prevCol,"B2T RIGHT",
//                                toGrid[prevRow - 1][prevCol], toGrid[row - 1][col],
//                                prevID, curID,
//                                toPrev, toCur, fromPrev, fromCur)
                                } else {
//                                    if (row > 0) {
//                                        printMeldTest(
//                                            row = row,
//                                            col = col,
//                                            prevRow = prevRow,
//                                            prevCol = prevCol,
//                                            msg = "B2T LEFT",
//                                            a = element1.key,
//                                            b = toGrid[row - 1][col],
//                                            c = element2.key,
//                                            d = toGrid[prevRow - 1][prevCol],
//                                            e = element3.key,
//                                            f = curID,
//                                            g = element4.key,
//                                            h = prevID,
//                                        )
//                                    }
                                    if ((row > 0) &&
                                        (element1.key == toGrid[row - 1][col]) &&
                                        (element2.key == toGrid[prevRow - 1][prevCol]) &&
                                        (element3.key == curID) &&
                                        (element4.key == prevID)
                                    ) {
                                        // From-bottom-to-top meld while traversing left.
                                        deltaRow = -1
                                        fromPrev = H_MELDS_DATA[element4.value][3]
                                        fromCur = H_MELDS_DATA[element4.value][2]
                                        toPrev = H_MELDS_DATA[element4.value][1]
                                        toCur = H_MELDS_DATA[element4.value][0]
//                            printMeld(row, col, prevRow, prevCol,"B2T LEFT",
//                                toGrid[row - 1][col], toGrid[prevRow - 1][prevCol],
//                                curID, prevID,
//                                toCur, toPrev, fromCur, fromPrev)
                                    } else continue
                                }
                            }
                        }

                        // Meld the knots
                        transferGrid[prevRow][prevCol] = fromPrev
                        transferGrid[row][col] = fromCur
                        transferGrid[prevRow + deltaRow][prevCol] = toPrev
                        transferGrid[row + deltaRow][col] = toCur

                        // Mark changes
                        changesGrid[prevRow][prevCol] = fromPrev
                        changesGrid[row][col] = fromCur
                        changesGrid[prevRow + deltaRow][prevCol] = toPrev
                        changesGrid[row + deltaRow][col] = toCur

                        // Delete old IDs
                        fromGrid[prevRow][prevCol] = BORDER_ID
                        fromGrid[row][col] = BORDER_ID

                        isMelded = true
                        break
                    }
                    if (isMelded) break
                }
                if (isMelded) break
            }
            if (isMelded) break
        }

        return isMelded
    }

    private fun entrance(id: Int): Int {
        return IN_2_OUT[id]!!.keys.first()
    }

    // TODO remove this for production
//    private fun id2String(id: Int): String {
//        return if ((id < 0) || (id >= BORDER_ID)) "." else (id + 'a'.code).toChar().toString()
//    }

    // TODO remove this for production
//    private fun printMeld(
//        row: Int,
//        col: Int,
//        prevRow: Int,
//        prevCol: Int,
//        msg: String,
//        a: Int,
//        b: Int,
//        c: Int,
//        d: Int,
//        e: Int,
//        f: Int,
//        g: Int,
//        h: Int,
//    ) {
//        println("${id2String(a)}${id2String(b)} -> ${id2String(e)}${id2String(f)}  CUR=$row/$col $msg ")
//        println("${id2String(c)}${id2String(d)}    ${id2String(g)}${id2String(h)} PREV=$prevRow/$prevCol")
//    }

    // TODO remove this for production
//    fun printMeldTest(
//        row: Int,
//        col: Int,
//        prevRow: Int,
//        prevCol: Int,
//        msg: String,
//        a: Int,
//        b: Int,
//        c: Int,
//        d: Int,
//        e: Int,
//        f: Int,
//        g: Int,
//        h: Int,
//    ) {
//        println("CUR=$row/$col PREV=$prevRow/$prevCol $msg ")
//        println("${id2String(a)} ?= ${id2String(b)}")
//        println("${id2String(c)} ?= ${id2String(d)}")
//        println("${id2String(e)} ?= ${id2String(f)}")
//        println("${id2String(g)} ?= ${id2String(h)}")
//    }

}
