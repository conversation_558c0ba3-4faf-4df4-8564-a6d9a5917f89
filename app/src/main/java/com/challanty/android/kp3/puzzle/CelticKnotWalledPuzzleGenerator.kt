package com.challanty.android.kp3.puzzle

import kotlin.random.Random
import com.challanty.android.kp3.util.*

/**
 * A puzzle generator that creates puzzles based on boundary walls.
 *
 * This generator uses a boundary matrix to determine the pattern for each cell in the puzzle.
 * Each cell in the boundary matrix specifies the walls bounding the pattern in the corresponding
 * puzzle matrix cell.
 *
 * The generator randomly places interior walls in the boundary matrix, which determines the
 * patterns in the puzzle matrix. The pattern at each cell is determined by:
 * - The walls in the corresponding boundary cell
 * - Whether the row is even/odd
 * - Whether the column is even/odd
 *
 * The resulting puzzle forms Celtic-knot-like looping paths that may go over and under themselves
 * and each other. A puzzle is considered solved when the picture shows only looping paths with
 * no dead ends.
 */
class CelticKnotWalledPuzzleGenerator {

    init {
        initWalls2ID()
    }

    fun generate(
        puzzle: CelticKnotPuzzle,
        isFlipOU: Boolean = false,
    ) {
        val solution = puzzle.solution

        if (solution.isEmpty()) return

        val rows = puzzle.rows
        val cols = puzzle.cols
        val seed = puzzle.seed

        // Create a random number generator, optionally with a seed
        val random = if (seed != null) Random(seed) else Random(System.currentTimeMillis())

        // Create a bounded wall matrix
        val wallMatrix = Array(rows) { row ->
            IntArray(cols) { col ->
                var walls = NO_WALLS

                // Add outer boundary walls
                if (row == 0) walls += TOP_WALL
                if (col == 0) walls += LFT_WALL
                if (row == rows - 1) walls += BOT_WALL
                if (col == cols - 1) walls += RHT_WALL

                walls
            }
        }

        // Place interior walls according to the Celtic knot strategy
        // Place walls strategically to ensure a proper puzzle
        placeInteriorWalls(wallMatrix, random)

        // Create the puzzle matrix based on the boundary matrix
        for (row in 0 until rows) {
            for (col in 0 until cols) {
                // Determine picture unit based on boundary walls and row/col parity
                solution[row][col] = determinePicUnit(wallMatrix, row, col, isFlipOU)
            }
        }
    }

    private fun placeInteriorWalls(
        wallMatrix: Array<IntArray>,
        random: Random
    ) {
        val rows = wallMatrix.size
        val cols = wallMatrix[0].size

        // Create a list of all possible placement positions
        var maxPlacements = 0
        val placementPositions = mutableListOf<Pair<Int, Int>>()
        for (row in 0 until rows - 1) {
            for (col in 0 until cols - 1) {
                // Only consider positions where (row + col) is odd.
                // That is, row and col are not both even or both odd.
                if ((row + col) % 2 == 1) {
                    placementPositions.add(Pair(row, col))
                    ++maxPlacements
                }
            }
        }

        // Randomly choose the number of wall placements to make (experience shows
        // between 1/3 of max and max is good)
        val numPlacements = random.nextInt(maxPlacements / 3, maxPlacements + 1)

        // Make random placements
        placementPositions.shuffle(random)
        for (i in 0 until numPlacements) {
            val (row, col) = placementPositions[i]

            // Randomly decide whether to place vertical or horizontal walls
            val isVertical = random.nextBoolean()

            if (isVertical) {
                // Place vertical walls
                wallMatrix[row][col] += RHT_WALL
                wallMatrix[row][col + 1] += LFT_WALL
                wallMatrix[row + 1][col] += RHT_WALL
                wallMatrix[row + 1][col + 1] += LFT_WALL
            } else {
                // Place horizontal walls
                wallMatrix[row][col] += BOT_WALL
                wallMatrix[row][col + 1] += BOT_WALL
                wallMatrix[row + 1][col] += TOP_WALL
                wallMatrix[row + 1][col + 1] += TOP_WALL
            }
        }
    }

    private fun determinePicUnit(
        wallMatrix: Array<IntArray>,
        row: Int,
        col: Int,
        isFlipOU: Boolean
    ): Int {
        // Get the walls for this cell
        val walls = wallMatrix[row][col]

        // Determine the row and column parity (even/odd)
        // If isFlipOU is true, flip the parity by adding 1 before taking modulo
        val rowParity = if (isFlipOU) (row + 1) % 2 else row % 2
        val colParity = if (isFlipOU) (col + 1) % 2 else col % 2

        // Look up the picture Unit ID in our table
        return WALLS_2_ID[rowParity][colParity][walls]
    }

    private fun initWalls2ID() {
        // Initialize the walls2ID lookup table
        val er = 0 // even row
        val or = 1 // odd row
        val ec = 0 // even column
        val oc = 1 // odd column

        // Single walls
        WALLS_2_ID[er][oc][RHT_WALL] = 0  // These are pattern IDs
        WALLS_2_ID[or][oc][BOT_WALL] = 1
        WALLS_2_ID[or][ec][LFT_WALL] = 2
        WALLS_2_ID[er][ec][TOP_WALL] = 3

        WALLS_2_ID[or][ec][RHT_WALL] = 4  // Opposite row/col parity of 0-3
        WALLS_2_ID[er][ec][BOT_WALL] = 5
        WALLS_2_ID[er][oc][LFT_WALL] = 6
        WALLS_2_ID[or][oc][TOP_WALL] = 7

        WALLS_2_ID[or][oc][LFT_WALL] = 8
        WALLS_2_ID[or][ec][TOP_WALL] = 9
        WALLS_2_ID[er][ec][RHT_WALL] = 10
        WALLS_2_ID[er][oc][BOT_WALL] = 11

        WALLS_2_ID[er][ec][LFT_WALL] = 12  // Opposite row/col parity of 8-11
        WALLS_2_ID[er][oc][TOP_WALL] = 13
        WALLS_2_ID[or][oc][RHT_WALL] = 14
        WALLS_2_ID[or][ec][BOT_WALL] = 15

        // No walls
        WALLS_2_ID[or][ec][NO_WALLS] = 16
        WALLS_2_ID[er][ec][NO_WALLS] = 17
        WALLS_2_ID[er][oc][NO_WALLS] = 18
        WALLS_2_ID[or][oc][NO_WALLS] = 19

        // Corner walls
        WALLS_2_ID[er][ec][TOP_LFT_COR] = 20
        WALLS_2_ID[er][oc][TOP_LFT_COR] = 20
        WALLS_2_ID[or][oc][TOP_LFT_COR] = 20
        WALLS_2_ID[or][ec][TOP_LFT_COR] = 20

        WALLS_2_ID[er][ec][TOP_RHT_COR] = 21
        WALLS_2_ID[er][oc][TOP_RHT_COR] = 21
        WALLS_2_ID[or][oc][TOP_RHT_COR] = 21
        WALLS_2_ID[or][ec][TOP_RHT_COR] = 21

        WALLS_2_ID[er][ec][BOT_RHT_COR] = 22
        WALLS_2_ID[er][oc][BOT_RHT_COR] = 22
        WALLS_2_ID[or][oc][BOT_RHT_COR] = 22
        WALLS_2_ID[or][ec][BOT_RHT_COR] = 22

        WALLS_2_ID[er][ec][BOT_LFT_COR] = 23
        WALLS_2_ID[er][oc][BOT_LFT_COR] = 23
        WALLS_2_ID[or][oc][BOT_LFT_COR] = 23
        WALLS_2_ID[or][ec][BOT_LFT_COR] = 23

        // Hallways
        WALLS_2_ID[er][ec][V_HALLWAY] = 24
        WALLS_2_ID[er][oc][V_HALLWAY] = 24
        WALLS_2_ID[or][oc][V_HALLWAY] = 24
        WALLS_2_ID[or][ec][V_HALLWAY] = 24

        WALLS_2_ID[er][ec][H_HALLWAY] = 25
        WALLS_2_ID[er][oc][H_HALLWAY] = 25
        WALLS_2_ID[or][oc][H_HALLWAY] = 25
        WALLS_2_ID[or][ec][H_HALLWAY] = 25
    }
}