package com.challanty.android.kp3.data.repository

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.core.IOException
import androidx.datastore.dataStore
import com.challanty.android.kp3.data.Saved
import com.challanty.android.kp3.data.Settings
import com.challanty.android.kp3.util.APP_VERSION
import com.challanty.android.kp3.util.DEFAULT_ANIMATE
import com.challanty.android.kp3.util.DEFAULT_ANIMATE_ROTATION
import com.challanty.android.kp3.util.DEFAULT_ANIMATE_SWAP
import com.challanty.android.kp3.util.DEFAULT_SINGLE_KNOT
import com.challanty.android.kp3.util.DEFAULT_BOARD
import com.challanty.android.kp3.util.DEFAULT_BOARD_COLS
import com.challanty.android.kp3.util.DEFAULT_BOARD_ROWS
import com.challanty.android.kp3.util.DEFAULT_LOCK_PERCENT
import com.challanty.android.kp3.util.DEFAULT_SOLUTION
import com.challanty.android.kp3.util.DEFAULT_TILES_ROTATABLE
import com.challanty.android.kp3.util.DEFAULT_TILE_COLS
import com.challanty.android.kp3.util.DEFAULT_TILE_LOCKS
import com.challanty.android.kp3.util.DEFAULT_TILE_ROWS
import com.challanty.android.kp3.util.twoDintArray2ByteString
import com.google.protobuf.ByteString
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

private val Context.settingsDataStore: DataStore<Settings> by dataStore(
    fileName = "settings.pb",
    serializer = SettingsSerializer
)

private val Context.savedDataStore: DataStore<Saved> by dataStore(
    fileName = "saved.pb",
    serializer = SavedSerializer
)

@Singleton
class Repository @Inject constructor(
    @ApplicationContext private val context: Context
) {

    private val settingsDataStore = context.settingsDataStore
    private val savedDataStore = context.savedDataStore

    private val repositoryScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    init {
        repositoryScope.launch {
            initRepository()
        }
    }

    private suspend fun initRepository() {
        try {
            if (settingsDataStore.data.first().version == 0) {
                initSettings()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        try {
            if (savedDataStore.data.first().board.isEmpty) {
                initSaved()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

// == Settings DataStore Section ==

    // Hold user changes to settings until the user is done making change
    private var pendingSettings: Settings = Settings.getDefaultInstance()

    val settingsDataStoreStateFlow: StateFlow<Settings> = settingsDataStore.data
        .catch { exception ->
            if (exception is IOException) {
                emit(Settings.getDefaultInstance())
            } else {
                throw exception
            }
        }
        .stateIn(
            scope = repositoryScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = Settings.getDefaultInstance()
        )

    fun pendingSetVersion(value: Int) {
        pendingSettings = pendingSettings.toBuilder().setVersion(value).build()
    }

    fun pendingSetBoardRows(value: Int) {
        pendingSettings = pendingSettings.toBuilder().setBoardRows(value).build()
    }

    fun pendingSetBoardCols(value: Int) {
        pendingSettings = pendingSettings.toBuilder().setBoardCols(value).build()
    }

    fun pendingSetTileRows(value: Int) {
        pendingSettings = pendingSettings.toBuilder().setTileRows(value).build()
    }

    fun pendingSetTileCols(value: Int) {
        pendingSettings = pendingSettings.toBuilder().setTileCols(value).build()
    }

    fun pendingSetTilesRotatable(value: Boolean) {
        pendingSettings = pendingSettings.toBuilder().setTilesRotatable(value).build()
    }

    fun pendingSetAnimate(value: Boolean) {
        pendingSettings = pendingSettings.toBuilder().setAnimate(value).build()
    }

    fun pendingSetAnimateRotation(value: Boolean) {
        pendingSettings = pendingSettings.toBuilder().setAnimateRotation(value).build()
    }

    fun pendingSetAnimateSwap(value: Boolean) {
        pendingSettings = pendingSettings.toBuilder().setAnimateSwap(value).build()
    }

    fun pendingSetSingleKnot(value: Boolean) {
        pendingSettings = pendingSettings.toBuilder().setSingleKnot(value).build()
    }

    fun pendingSetLockPercent(value: Int) {
        pendingSettings = pendingSettings.toBuilder().setLockPercent(value).build()
    }

    suspend fun onPendingFinalized() {
        settingsDataStore.updateData { pendingSettings }
    }

    suspend fun initSettings() {
        try {
            settingsDataStore.updateData { currentSettings ->
                currentSettings.toBuilder()
                    .setVersion(APP_VERSION)
                    .setBoardRows(DEFAULT_BOARD_ROWS)
                    .setBoardCols(DEFAULT_BOARD_COLS)
                    .setTileRows(DEFAULT_TILE_ROWS)
                    .setTileCols(DEFAULT_TILE_COLS)
                    .setLockPercent(DEFAULT_LOCK_PERCENT)
                    .setTilesRotatable(DEFAULT_TILES_ROTATABLE)
                    .setAnimate(DEFAULT_ANIMATE)
                    .setAnimateRotation(DEFAULT_ANIMATE_ROTATION)
                    .setAnimateSwap(DEFAULT_ANIMATE_SWAP)
                    .setSingleKnot(DEFAULT_SINGLE_KNOT)
                    .build()
            }
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

// == Saved DataStore Section ==

    val savedDataStoreStateFlow: StateFlow<Saved> = savedDataStore.data
        .catch { exception ->
            if (exception is IOException) {
                emit(Saved.getDefaultInstance())
            } else {
                throw exception
            }
        }
        .stateIn(
            scope = repositoryScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = Saved.getDefaultInstance()
        )

    suspend fun setBoard(value: ByteString) {
        try {
            savedDataStore.updateData { currentSettings ->
                currentSettings.toBuilder()
                    .setBoard(value)
                    .build()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    suspend fun setSolution(value: ByteString) {
        try {
            savedDataStore.updateData { currentSettings ->
                currentSettings.toBuilder()
                    .setSolution(value)
                    .build()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    suspend fun setLockedTiles(value: ByteString) {
        try {
            savedDataStore.updateData { currentSettings ->
                currentSettings.toBuilder()
                    .setLockedTiles(value)
                    .build()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    suspend fun initSaved() {
        try {
            savedDataStore.updateData { currentSettings ->
                currentSettings.toBuilder()
                    .setBoard(twoDintArray2ByteString(DEFAULT_BOARD))
                    .setSolution(twoDintArray2ByteString(DEFAULT_SOLUTION))
                    .setLockedTiles(twoDintArray2ByteString(DEFAULT_TILE_LOCKS))
                    .build()
            }
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }
}
