<resources>
    <!-- App name -->
    <string name="app_name">KnotPuzzled</string>

    <!-- Screen titles -->
    <string name="screen_title_loading">KnotPuzzled</string>
    <string name="screen_title_game">KnotPuzzled</string>
    <string name="screen_title_settings">KnotPuzzled Settings</string>
    <string name="screen_title_help">KnotPuzzled Help</string>
    <string name="screen_title_about">KnotPuzzled About</string>

    <!-- Navigation bar items -->
    <string name="nav_game">Game</string>
    <string name="nav_settings">Settings</string>
    <string name="nav_help">Help</string>
    <string name="nav_about">About</string>

    <!-- Settings screen -->
    <string name="settings_title">Game Settings</string>
    <string name="settings_board_dimensions">Board Dimensions</string>
    <string name="settings_board_rows">Board Rows: %1$d</string>
    <string name="settings_board_cols">Board Columns: %1$d</string>
    <string name="settings_current_board_size">Board Size: %1$d × %2$d</string>
    <string name="settings_tile_dimensions">Tile Dimensions</string>
    <string name="settings_tile_rows">Tile Rows: %1$d</string>
    <string name="settings_tile_columns">Tile Columns: %1$d</string>
    <string name="settings_board_constraint_explanation">Board rows and columns cannot both be 1 at the same time.</string>
    <string name="settings_tile_constraint_explanation">When board rows (or columns) is odd, tile rows (or columns) must be even.</string>
    <string name="settings_rotatable_tiles">Allow Rotating Tiles</string>
    <string name="settings_rotatable_constraint">Tiles can only be rotated when they have the same number of rows and columns (square tiles).</string>
    <string name="settings_animation">Animation Settings</string>
    <string name="settings_animate">Enable Animations</string>
    <string name="settings_animate_rotation">Animate Tile Rotations</string>
    <string name="settings_animate_swap">Animate Tile Swaps</string>
    <string name="settings_single_knot">Single Knot</string>

    <!-- Game screen -->
    <string name="remaining_tile_locks">Remaining Tile Locks</string>
    <string name="game_new_game">New Game</string>

    <!-- Help screen -->
    <string name="collapse">Collapse</string>
    <string name="expand">Expand</string>
    <string name="help_title_1">The Game</string>
    <string name="help_text_1">Unscramble a picture by rearranging tiles.</string>
    <string name="help_title_2">How to Play</string>
    <string name="help_text_2">Swap and rotate the board tiles until the picture is unscrambled.\n\nBy default, tiles you unscramble turn gray, lock into place, and do not respond to taps.</string>
    <string name="help_title_3">How to Swap Tiles</string>
    <string name="help_text_3">Tap two tiles. They will highlight, swap positions, and de-highlight.\n\nIf you change your mind after tapping a tile, tap it again to deselect it.</string>
    <string name="help_title_4">How to Rotate Tiles</string>
    <string name="help_text_4">Double-tap or long-press a tile. It will rotate clock-wise one quarter-turn. Also, any highlighted tile de-highlights.\n\nBy default, tiles do not require rotating to unscramble the picture and double-taps and long-presses are ignored.</string>
    <string name="help_title_5">Game End</string>
    <string name="help_text_5">A game ends when the board shows a picture of one or more ribbon loops with no loose ends.\n\nWhen the game ends the board and tile borders disappear and the picture changes color.</string>
    <string name="help_title_6">How to Start a New Game</string>
    <string name="help_text_6">- Tap the plus sign icon (+) in the upper-right corner of the Game Screen, or\n- Tap an unscrambled picture, or\n- Change any game setting other than the Automation Settings.</string>
    <string name="help_title_7">Screens</string>
    <string name="help_text_7">Tap an icon at the bottom of the display to switch to the indicated screen.\n\n- The Game screen is for playing the game.\n- The Settings screen is for customizing the game.\n- The Help screen is the screen you are now reading.\n- The About screen shows the game version number and copyright notices.\n\nThe Settings and Help screens can be scrolled when needed.</string>
    <string name="help_title_8">Customization</string>
    <string name="help_text_8">Game features can be customized to vary game difficulty and add variety.\n\nChanging any of the game settings other than the Animation Settings automatically starts a new game.</string>
    <string name="help_title_9">Rotating Tiles</string>
    <string name="help_text_9">When Rotating Tiles is turned on, some tiles may need to be rotated to unscramble the picture. Double-tap or long-press a tile to make it rotate clockwise one quarter-turn.\n\nRotating Tiles increases game difficulty.</string>
    <string name="help_title_10">Tile Rows</string>
    <string name="help_text_10">Tiles can have from 1 to 6 rows. Each row adds detail to the ribbon segments displayed in the tile.\n\nSmaller tiles increases game difficulty.</string>
    <string name="help_title_11">Tile Columns</string>
    <string name="help_text_11">Tiles can have from 1 to 6 columns. Each column adds detail to the ribbon segments displayed in the tile.\n\nWhen Rotating Tiles is turned on, Tile Rows is automatically forced to be the same as Tile Columns.\n\nSmaller tiles increase game difficulty.</string>
    <string name="help_title_12">Board Rows</string>
    <string name="help_text_12">The board can have from 1 to 6 rows of tiles.\n\nDue to the nature of the picture, the number of rows in a tile is automatically forced to be an even number (2, 4, or 6) when the board rows is an odd number (1, 3, or 5).\n\nLarger boards increase game difficulty.</string>
    <string name="help_title_13">Board Columns</string>
    <string name="help_text_13">The board can have from 1 to 6 columns of tiles.\n\nDue to the nature of the picture, the number of columns in a tile is automatically forced to be an even number (2, 4 or 6) when the board columns is an odd number (1, 3, or 5).\n\nLarger board increase game difficulty.</string>
    <string name="help_title_14">Locks</string>
    <string name="help_text_14">By default, tiles put into their solution position turn grey, lock into place, and do not respond to taps.\n\nA picture can have multiple solutions, but only one solution is used to determine whether a tile is locked.\n\nThe number of tiles remaining to be locked is shown in the upper-right corner of the Game Screen.\n\nLocks decrease game difficulty.</string>
    <string name="help_title_15">Lock Percentage</string>
    <string name="help_text_15">From 0 to 100 percent of the tiles can be locked.\n\nLower Lock Percentages increase game difficulty.</string>
    <string name="help_title_16">Animations</string>
    <string name="help_text_16">By default, animations are played when tiles are swapped or rotated and at game end. Game play can be faster with fewer animations turned on.</string>
    <string name="help_title_17">Animate Game End</string>
    <string name="help_text_17">Turn on to animate the end of a game.</string>
    <string name="help_title_18">Animate Tile Swaps</string>
    <string name="help_text_18">Turn on to animate the swapping of tiles.</string>
    <string name="help_title_19">Animate Tile Rotations</string>
    <string name="help_text_19">Turn on to animate the rotating of tiles.</string>

    <!-- About screen -->
    <string name="about_text">KnotPuzzled version 3.0\nCopyright © 2025\nChallanty Interactive Entertainment</string>
    <string name="analytics_text">This software uses Google Analytics to collect anonymous usage, performance, and error information.</string>

    <!-- Content descriptions -->
    <string name="content_description_won_game_tile">SWon Tile %1$s</string>
    <string name="content_description_game_tile">Game Tile %1$s</string>
    <string name="content_description_background_image">Background Celtic knot pattern</string>

    <!-- Animation labels -->
    <string name="animation_label_background_color">Background Color Animation</string>
    <string name="animation_label_tint_color">Tint Color Animation</string>
</resources>