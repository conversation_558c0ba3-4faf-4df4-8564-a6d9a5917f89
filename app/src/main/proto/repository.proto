syntax = "proto3";

option java_package = "com.challanty.android.kp3.data";
option java_multiple_files = true;

message Settings {
  int32 version = 1;
  int32 board_rows = 2;
  int32 board_cols = 3;
  int32 tile_rows = 4;
  int32 tile_cols = 5;
  int32 lock_percent = 6;
  bool tiles_rotatable = 7;
  bool animate = 8;
  bool animate_rotation = 9;
  bool animate_swap = 10;
  bool single_knot = 11;
}

message Saved {
  bytes board = 1;
  bytes solution = 2;
  bytes locked_tiles = 3;
}
